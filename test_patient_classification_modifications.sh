#!/bin/bash

echo "=========================================="
echo "患者分类逻辑修改功能测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if eval "$test_command"; then
        if [ "$expected_result" = "pass" ]; then
            echo -e "${GREEN}✓ 通过${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ 失败 (期望失败但实际通过)${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            echo -e "${GREEN}✓ 通过 (期望失败)${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ 失败${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi
    echo ""
}

echo -e "${YELLOW}1. 检查核心文件是否存在${NC}"
echo "----------------------------------------"

run_test "SyncConstants.java 存在" "[ -f 'src/main/java/com/sysgetway/core/common/constant/SyncConstants.java' ]" "pass"
run_test "SyncServiceImpl.java 存在" "[ -f 'src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java' ]" "pass"
run_test "SyncController.java 存在" "[ -f 'src/main/java/com/sysgetway/core/controller/SyncController.java' ]" "pass"
run_test "Patient.java 存在" "[ -f 'src/main/java/com/sysgetway/core/entity/Patient.java' ]" "pass"

echo -e "${YELLOW}2. 检查新的Redis键前缀${NC}"
echo "----------------------------------------"

run_test "InPatient- 前缀存在" "grep -q 'InPatient-' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"
run_test "OutPatient- 前缀存在" "grep -q 'OutPatient-' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"
run_test "UpData- 前缀存在" "grep -q 'UpData-' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"

echo -e "${YELLOW}3. 检查患者分类相关方法${NC}"
echo "----------------------------------------"

run_test "handleAutoPatientClassification 方法存在" "grep -q 'handleAutoPatientClassification' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "storePatientDataWithNewFormat 方法存在" "grep -q 'storePatientDataWithNewFormat' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "convertPatientToStorageFormat 方法存在" "grep -q 'convertPatientToStorageFormat' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "processPatientDataWithAutoClassification 方法存在" "grep -q 'processPatientDataWithAutoClassification' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"

echo -e "${YELLOW}4. 检查工具类和常量${NC}"
echo "----------------------------------------"

run_test "PatientKeyGenerator 类存在" "grep -q 'PatientKeyGenerator' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"
run_test "getPatientKeyPrefix 方法存在" "grep -q 'getPatientKeyPrefix' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"
run_test "isStorageType 方法存在" "grep -q 'isStorageType' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"
run_test "generatePatientKey 方法存在" "grep -q 'generatePatientKey' src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" "pass"

echo -e "${YELLOW}5. 检查auto分类逻辑${NC}"
echo "----------------------------------------"

run_test "auto类型检查逻辑存在" "grep -q 'SyncConstants.PatientType.AUTO.equals(patientType)' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "status=1 分类逻辑存在" "grep -q 'case 1:' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "status=2 分类逻辑存在" "grep -q 'case 2:' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "IN和UP同时添加逻辑存在" "grep -A5 'case 1:' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java | grep -q 'PatientType.IN' && grep -A5 'case 1:' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java | grep -q 'PatientType.UP'" "pass"

echo -e "${YELLOW}6. 检查新的存储格式${NC}"
echo "----------------------------------------"

run_test "对象存储方式存在" "grep -q 'hSet.*patientData' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "患者唯一标识生成存在" "grep -q 'generatePatientKey' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"
run_test "时间戳转换存在" "grep -q 'toEpochSecond' src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" "pass"

echo -e "${YELLOW}7. 检查接口文档更新${NC}"
echo "----------------------------------------"

run_test "接口注释包含新存储格式说明" "grep -q 'InPatient-住院号' src/main/java/com/sysgetway/core/controller/SyncController.java" "pass"
run_test "接口注释包含auto分类说明" "grep -q 'status=1.*同时存储.*in.*up' src/main/java/com/sysgetway/core/controller/SyncController.java" "pass"

echo -e "${YELLOW}8. 检查测试文件${NC}"
echo "----------------------------------------"

run_test "患者分类测试文件存在" "[ -f 'src/test/java/com/sysgetway/core/service/PatientClassificationTest.java' ]" "pass"
run_test "控制器集成测试文件存在" "[ -f 'src/test/java/com/sysgetway/core/controller/SyncControllerIntegrationTest.java' ]" "pass"

echo -e "${YELLOW}9. 检查编译状态${NC}"
echo "----------------------------------------"

run_test "SyncServiceImpl.class 存在" "[ -f 'target/classes/com/sysgetway/core/service/impl/SyncServiceImpl.class' ]" "pass"
run_test "SyncConstants.class 存在" "[ -f 'target/classes/com/sysgetway/core/common/constant/SyncConstants.class' ]" "pass"

echo "=========================================="
echo -e "${BLUE}测试结果汇总${NC}"
echo "=========================================="
echo -e "总测试数: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！患者分类逻辑修改成功完成。${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关问题。${NC}"
    exit 1
fi
