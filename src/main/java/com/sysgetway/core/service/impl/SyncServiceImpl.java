package com.sysgetway.core.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.sysgetway.core.common.constant.ResultCode;
import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.exception.BusinessException;
import com.sysgetway.core.common.util.AsyncTaskManager;
import com.sysgetway.core.common.util.RedisUtils;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;
import com.sysgetway.core.model.dto.SyncResultDTO;
import com.sysgetway.core.service.SyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.UUID;
import org.springframework.beans.BeanUtils;
import java.lang.reflect.Field;
import java.util.Objects;

/**
 * 同步服务实现类
 */
@Service
@Slf4j
public class SyncServiceImpl implements SyncService {

    @Resource
    private RedisUtils redisUtils;
    
    @Resource
    private AsyncTaskManager asyncTaskManager;
    
    @Resource(name = "syncTaskExecutor")
    private java.util.concurrent.Executor syncTaskExecutor;
    
    // 日期时间格式化器 - 修改为支持微秒的格式
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss[.SSSSSS]");
    
    // 备用日期时间格式化器，用于处理不同格式的日期时间字符串
    private static final DateTimeFormatter[] BACKUP_FORMATTERS = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ISO_LOCAL_DATE_TIME
    };
    
    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;
    
    @Override
    public ResponseResult<SyncResultDTO> syncFull(String hospitalId, String tableName) {
        log.info("开始全量同步医院[{}]的[{}]数据", hospitalId, tableName);
        
        // 验证表名是否有效
        if (!SyncConstants.TableName.isValid(tableName)) {
            log.error("无效的表名: {}", tableName);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的表名: " + tableName);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.FULL);
        
        // 检查是否有正在运行的任务
        if (asyncTaskManager.hasTask(taskKey)) {
            log.info("医院[{}]的[{}]全量同步任务正在进行中", hospitalId, tableName);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, tableName);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType(tableName)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 异步执行同步任务
        CompletableFuture<SyncResultDTO> future = syncFullAsync(hospitalId, tableName);
        // 注册任务到任务管理器
        asyncTaskManager.registerTask(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, initialResult);
                
        return ResponseResult.success(initialResult);
    }

    @Override
    public ResponseResult<SyncResultDTO> syncFullAsync(String hospitalId, String tableName, List<Object> dataList) {
        log.info("开始异步全量同步医院[{}]的[{}]数据，数据量: {}", hospitalId, tableName,
                (dataList != null ? dataList.size() : "从外部系统获取"));

        // 验证表名是否有效
        if (!SyncConstants.TableName.isValid(tableName)) {
            log.error("无效的表名: {}", tableName);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的表名: " + tableName);
        }

        // 生成任务ID
        String taskId = UUID.randomUUID().toString();
        log.info("生成全量同步任务ID: {}", taskId);

        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.FULL);

        // 创建初始结果
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .taskId(taskId)
                .build();

        // 保存初始状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, initialResult);
        saveTaskStatusToRedis(taskId, initialResult);

        // 使用CompletableFuture.supplyAsync确保真正的异步执行
        CompletableFuture<SyncResultDTO> future = CompletableFuture.supplyAsync(() -> {
            try {
                log.info("异步全量同步任务开始执行，任务ID: {}, 线程: {}", taskId, Thread.currentThread().getName());
                return executeFullSyncTask(hospitalId, tableName, dataList, taskId);
            } catch (Exception e) {
                log.error("异步全量同步任务执行异常，任务ID: {}, 异常: {}", taskId, e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }, syncTaskExecutor); // 使用指定的线程池

        // 注册任务到任务管理器，但不等待完成
        asyncTaskManager.registerTask(taskKey, future);

        // 异步更新任务完成状态
        future.whenComplete((result, throwable) -> {
            try {
                if (throwable != null) {
                    log.error("全量同步任务执行失败，任务ID: {}, 异常: {}", taskId, throwable.getMessage(), throwable);

                    SyncResultDTO errorResult = SyncResultDTO.builder()
                            .status(SyncConstants.SyncStatus.FAILED)
                            .message(SyncConstants.SyncMessage.FAILED)
                            .hospitalId(hospitalId)
                            .tableNameOrPatientType(tableName)
                            .startTime(initialResult.getStartTime())
                            .endTime(LocalDateTime.now())
                            .taskId(taskId)
                            .errorMessage(throwable.getMessage())
                            .build();
                    errorResult.setCostTime(Duration.between(errorResult.getStartTime(), errorResult.getEndTime()).toMillis());

                    saveSyncStatusToRedis(hospitalId, tableName, errorResult);
                    saveTaskStatusToRedis(taskId, errorResult);
                } else {
                    log.info("全量同步任务执行成功，任务ID: {}, 数据量: {}", taskId, result.getCount());

                    saveSyncStatusToRedis(hospitalId, tableName, result);
                    saveTaskStatusToRedis(taskId, result);
                }
            } catch (Exception e) {
                log.error("更新全量同步任务完成状态时发生异常: {}", e.getMessage(), e);
            }
        });

        log.info("全量同步任务已启动，立即返回任务ID: {}", taskId);
        return ResponseResult.success(initialResult);
    }

    /**
     * 异步执行全量同步
     */
    @Async("syncTaskExecutor")
    public CompletableFuture<SyncResultDTO> syncFullAsync(String hospitalId, String tableName) {
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .build();
        
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, result);
        
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.FULL);
        
        try {
            // 模拟获取数据的耗时操作
            List<?> dataList = mockFetchData(tableName);
            
            if (dataList == null || dataList.isEmpty()) {
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.NO_DATA);
                result.setCount(0);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            } else {
                // 将数据存入Redis
                saveDataToRedis(hospitalId, tableName, dataList);
                
                // 更新结果
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                result.setCount(dataList.size());
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                
                // 更新最后同步时间
                saveLastSyncTimeToRedis(hospitalId, tableName, result.getEndTime());
            }
            
            log.info("医院[{}]的[{}]数据全量同步完成，数据量: {}", hospitalId, tableName, result.getCount());
        } catch (Exception e) {
            log.error("医院[{}]的[{}]数据全量同步失败: {}", hospitalId, tableName, e.getMessage(), e);
            
            // 尝试重试
            int currentRetryCount = asyncTaskManager.incrementRetryCount(taskKey);
            
            if (currentRetryCount <= MAX_RETRY_COUNT) {
                log.info("医院[{}]的[{}]数据全量同步失败，第{}次重试", hospitalId, tableName, currentRetryCount);
                // 递归重试
                return syncFullAsync(hospitalId, tableName);
            }
            
            // 重试次数用完，记录失败
            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            result.setRetryCount(currentRetryCount);
        } finally {
            // 更新同步状态到Redis
            try {
                saveSyncStatusToRedis(hospitalId, tableName, result);
            } catch (Exception e) {
                log.error("保存同步状态到Redis失败: {}", e.getMessage(), e);
            }
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Override
    public ResponseResult<SyncResultDTO> syncIncremental(String hospitalId, String tableName) {
        log.info("开始增量同步医院[{}]的[{}]数据", hospitalId, tableName);
        
        // 验证表名是否有效
        if (!SyncConstants.TableName.isValid(tableName)) {
            log.error("无效的表名: {}", tableName);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的表名: " + tableName);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.INCREMENTAL);
        
        // 检查是否有正在运行的任务
        if (asyncTaskManager.hasTask(taskKey)) {
            log.info("医院[{}]的[{}]增量同步任务正在进行中", hospitalId, tableName);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, tableName);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType(tableName)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 获取最后同步时间
        LocalDateTime lastSyncTime = getLastSyncTimeFromRedis(hospitalId, tableName);
        if (lastSyncTime == null) {
            // 如果没有最后同步时间，执行全量同步
            log.info("医院[{}]的[{}]数据没有最后同步时间，将执行全量同步", hospitalId, tableName);
            return syncFull(hospitalId, tableName);
        }
        
        // 异步执行增量同步任务
        CompletableFuture<SyncResultDTO> future = syncIncrementalAsync(hospitalId, tableName, lastSyncTime);
        // 注册任务到任务管理器
        asyncTaskManager.registerTask(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .lastSyncTime(lastSyncTime)
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, initialResult);
                
        return ResponseResult.success(initialResult);
    }
    
    /**
     * 异步执行增量同步
     */
    @Async("syncTaskExecutor")
    public CompletableFuture<SyncResultDTO> syncIncrementalAsync(String hospitalId, String tableName, LocalDateTime lastSyncTime) {
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .lastSyncTime(lastSyncTime)
                .build();
        
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, result);
        
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.INCREMENTAL);
        
        try {
            // 模拟获取增量数据
            List<?> dataList = mockFetchIncrementalData(tableName, lastSyncTime);
            
            if (dataList == null || dataList.isEmpty()) {
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.NO_DATA);
                result.setCount(0);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            } else {
                // 将数据存入Redis
                saveDataToRedis(hospitalId, tableName, dataList);
                
                // 更新结果
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                result.setCount(dataList.size());
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                
                // 更新最后同步时间
                saveLastSyncTimeToRedis(hospitalId, tableName, result.getEndTime());
            }
            
            log.info("医院[{}]的[{}]数据增量同步完成，数据量: {}", hospitalId, tableName, result.getCount());
        } catch (Exception e) {
            log.error("医院[{}]的[{}]数据增量同步失败: {}", hospitalId, tableName, e.getMessage(), e);
            
            // 尝试重试
            int currentRetryCount = asyncTaskManager.incrementRetryCount(taskKey);
            
            if (currentRetryCount <= MAX_RETRY_COUNT) {
                log.info("医院[{}]的[{}]数据增量同步失败，第{}次重试", hospitalId, tableName, currentRetryCount);
                // 递归重试
                return syncIncrementalAsync(hospitalId, tableName, lastSyncTime);
            }
            
            // 重试次数用完，记录失败
            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            result.setRetryCount(currentRetryCount);
        } finally {
            // 更新同步状态到Redis
            try {
                saveSyncStatusToRedis(hospitalId, tableName, result);
            } catch (Exception e) {
                log.error("保存同步状态到Redis失败: {}", e.getMessage(), e);
            }
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Override
    public ResponseResult<SyncResultDTO> syncPatientByType(String hospitalId, String patientType, List<Patient> patients) {
        log.info("开始同步医院[{}]的[{}]类型患者数据，传入数据量: {}", hospitalId, patientType, 
                (patients != null ? patients.size() : 0));
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的患者类型: " + patientType);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 检查是否有正在运行的任务
        if (asyncTaskManager.hasTask(taskKey)) {
            log.info("医院[{}]的[{}]类型患者同步任务正在进行中", hospitalId, patientType);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, "patient:" + patientType);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType("patient:" + patientType)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 添加样本患者数据日志输出
        if (patients != null && !patients.isEmpty()) {
            int sampleSize = Math.min(5, patients.size());
            log.info("样本患者数据（前{}条）:", sampleSize);
            for (int i = 0; i < sampleSize; i++) {
                Patient patient = patients.get(i);
                log.info("患者[{}] - ID: {}, 姓名: {}, 状态: {}, 入院时间: {}, 出院时间: {}",
                        i + 1, 
                        patient.getInpatientInfoId(),
                        patient.getName(),
                        patient.getStatus(),
                        patient.getInhospitalTime(),
                        patient.getOuthospitalTime());
            }
        }
        
        // 生成唯一任务ID
        String taskId = UUID.randomUUID().toString();
        log.info("生成任务ID: {}", taskId);
        
        // 异步执行患者分类同步任务，传入患者数据
        CompletableFuture<SyncResultDTO> future = syncPatientByTypeAsyncInternal(hospitalId, patientType, patients, taskId);
        // 注册任务到任务管理器
        asyncTaskManager.registerTask(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .taskId(taskId)  // 添加任务ID
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, initialResult);
        
        // 同时保存任务状态到Redis，使用任务ID作为键
        saveTaskStatusToRedis(taskId, initialResult);
                
        return ResponseResult.success(initialResult);
    }
    
    /**
     * 异步执行患者分类同步
     */
    @Async("syncTaskExecutor")
    public CompletableFuture<SyncResultDTO> syncPatientByTypeAsyncInternal(String hospitalId, String patientType, List<Patient> patients, String taskId) {
        log.info("启动异步患者分类同步任务，医院ID: {}，患者类型: {}, 数据量: {}",
                hospitalId, patientType, (patients != null ? patients.size() : 0));

        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的患者类型: " + patientType);
        }

        // 如果是auto类型，进行自动分类处理
        if (SyncConstants.PatientType.AUTO.equals(patientType)) {
            return handleAutoPatientClassification(hospitalId, patients, taskId);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 检查是否有正在运行的任务
        if (asyncTaskManager.hasTask(taskKey)) {
            log.info("医院[{}]的[{}]类型患者同步任务正在进行中", hospitalId, patientType);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, "patient:" + patientType);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType("patient:" + patientType)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return CompletableFuture.completedFuture(result);
        }
        
        // 使用传入的taskId
        log.info("使用任务ID: {}", taskId);
        
        // 创建初始状态结果
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .taskId(taskId)
                .build();
        
        // 保存初始状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, initialResult);
        saveTaskStatusToRedis(taskId, initialResult);
        
        // 使用CompletableFuture.supplyAsync确保真正的异步执行
        CompletableFuture<SyncResultDTO> future = CompletableFuture.supplyAsync(() -> {
            try {
                log.info("异步任务开始执行，任务ID: {}, 线程: {}", taskId, Thread.currentThread().getName());
                return executePatientSyncTask(hospitalId, patientType, patients, taskId);
            } catch (Exception e) {
                log.error("异步任务执行异常，任务ID: {}, 异常: {}", taskId, e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }, syncTaskExecutor); // 使用指定的线程池
        
        // 注册任务到任务管理器，但不等待完成
        asyncTaskManager.registerTask(taskKey, future);
        
        // 添加任务完成后的状态更新回调
        future.whenComplete((result, ex) -> {
            try {
                if (ex != null) {
                    log.error("患者分类同步任务执行异常，任务ID: {}, 异常: {}", taskId, ex.getMessage(), ex);
                    // 更新任务状态为失败
                    SyncResultDTO failedResult = SyncResultDTO.builder()
                            .status(SyncConstants.SyncStatus.FAILED)
                            .message(SyncConstants.SyncMessage.FAILED)
                            .errorMessage(ex.getMessage())
                            .hospitalId(hospitalId)
                            .tableNameOrPatientType("patient:" + patientType)
                            .startTime(initialResult.getStartTime())
                            .endTime(LocalDateTime.now())
                            .taskId(taskId)
                            .build();
                    if (failedResult.getStartTime() != null) {
                        failedResult.setCostTime(Duration.between(failedResult.getStartTime(), failedResult.getEndTime()).toMillis());
                    }
                    
                    saveSyncStatusToRedis(hospitalId, "patient:" + patientType, failedResult);
                    saveTaskStatusToRedis(taskId, failedResult);
                } else {
                    log.info("患者分类同步任务执行完成，任务ID: {}, 状态: {}, 线程: {}", taskId, result.getStatus(), Thread.currentThread().getName());
                    // 确保任务成功完成后状态已更新，防止任务一直显示"同步中"
                    if (result.getStatus() == null || result.getStatus() == SyncConstants.SyncStatus.IN_PROGRESS) {
                        log.info("任务完成但状态未更新，手动更新状态为成功，任务ID: {}", taskId);
                        result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                        result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                        result.setEndTime(LocalDateTime.now());
                        if (result.getStartTime() != null) {
                            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                        }
                        
                        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
                        saveTaskStatusToRedis(taskId, result);
                    }
                }
            } catch (Exception e) {
                log.error("更新任务完成状态时发生异常: {}", e.getMessage(), e);
            }
        });
        
        return CompletableFuture.completedFuture(initialResult);
    }

    /**
     * 处理auto类型患者的自动分类
     */
    private CompletableFuture<SyncResultDTO> handleAutoPatientClassification(String hospitalId, List<Patient> patients, String taskId) {
        log.info("开始处理auto类型患者自动分类，医院ID: {}，患者数量: {}", hospitalId, (patients != null ? patients.size() : 0));

        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:auto")
                .startTime(LocalDateTime.now())
                .taskId(taskId)
                .build();

        // 如果没有患者数据，直接返回成功
        if (patients == null || patients.isEmpty()) {
            result.setStatus(SyncConstants.SyncStatus.SUCCESS);
            result.setMessage(SyncConstants.SyncMessage.NO_DATA);
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(0L);
            return CompletableFuture.completedFuture(result);
        }

        // 按照患者状态进行分类
        Map<String, List<Patient>> classifiedPatients = new HashMap<>();
        classifiedPatients.put(SyncConstants.PatientType.IN, new ArrayList<>());
        classifiedPatients.put(SyncConstants.PatientType.UP, new ArrayList<>());
        classifiedPatients.put(SyncConstants.PatientType.OUT, new ArrayList<>());

        int totalProcessed = 0;
        int totalValid = 0;
        int totalSkipped = 0;

        for (Patient patient : patients) {
            totalProcessed++;

            // 验证患者数据的基本有效性
            if (patient.getHospitalizationNo() == null || patient.getHospitalizationNo().trim().isEmpty()) {
                log.debug("患者住院号为空，跳过: ID={}", patient.getInpatientInfoId());
                totalSkipped++;
                continue;
            }

            // 根据患者状态进行分类
            if (patient.getStatus() != null) {
                switch (patient.getStatus()) {
                    case 1:
                        // 在院患者：同时加入IN和UP分类
                        classifiedPatients.get(SyncConstants.PatientType.IN).add(patient);
                        classifiedPatients.get(SyncConstants.PatientType.UP).add(patient);
                        totalValid++;
                        break;
                    case 2:
                        // 出院患者：加入OUT分类
                        classifiedPatients.get(SyncConstants.PatientType.OUT).add(patient);
                        totalValid++;
                        break;
                    default:
                        log.debug("患者状态未知，跳过: ID={}, 状态={}", patient.getInpatientInfoId(), patient.getStatus());
                        totalSkipped++;
                        break;
                }
            } else {
                log.debug("患者状态为空，跳过: ID={}", patient.getInpatientInfoId());
                totalSkipped++;
            }
        }

        log.info("患者数据auto分类完成，总数: {}，有效: {}，跳过: {}，IN: {}，UP: {}，OUT: {}",
                totalProcessed, totalValid, totalSkipped,
                classifiedPatients.get(SyncConstants.PatientType.IN).size(),
                classifiedPatients.get(SyncConstants.PatientType.UP).size(),
                classifiedPatients.get(SyncConstants.PatientType.OUT).size());

        // 分别存储每个分类的患者数据
        int totalStoredCount = 0;
        Map<String, Integer> classificationStats = new HashMap<>();

        for (Map.Entry<String, List<Patient>> entry : classifiedPatients.entrySet()) {
            String patientType = entry.getKey();
            List<Patient> patientsOfType = entry.getValue();

            if (!patientsOfType.isEmpty()) {
                int storedCount = storePatientDataWithNewFormat(hospitalId, patientType, patientsOfType);
                totalStoredCount += storedCount;
                classificationStats.put(patientType, storedCount);
                log.info("患者类型[{}]数据存储完成，存储数量: {}", patientType, storedCount);
            } else {
                classificationStats.put(patientType, 0);
            }
        }

        // 更新结果
        result.setStatus(SyncConstants.SyncStatus.SUCCESS);
        result.setMessage(SyncConstants.SyncMessage.SUCCESS);
        result.setEndTime(LocalDateTime.now());
        result.setCostTime(result.getEndTime().toEpochSecond(java.time.ZoneOffset.UTC) * 1000 -
                          result.getStartTime().toEpochSecond(java.time.ZoneOffset.UTC) * 1000);
        result.setCount(totalStoredCount);
        result.setTotalCount(totalProcessed);
        result.setSuccessCount(totalValid);
        result.setSkippedCount(totalSkipped);
        result.setFailedCount(totalProcessed - totalValid);
        result.setClassificationStats(classificationStats);

        log.info("auto类型患者分类处理完成，总处理: {}，成功: {}，存储: {}，跳过: {}，失败: {}，耗时: {}ms",
                totalProcessed, totalValid, totalStoredCount, totalSkipped, (totalProcessed - totalValid), result.getCostTime());

        return CompletableFuture.completedFuture(result);
    }

    /**
     * 异步执行患者分类同步
     */
    @Async("syncTaskExecutor")
    public CompletableFuture<SyncResultDTO> syncPatientByTypeAsync(String hospitalId, String patientType) {
        try {
            // 模拟获取指定类型的患者数据
            List<Patient> patientList = mockFetchPatientByType(patientType);
            
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            log.info("生成任务ID: {}", taskId);
            
            // 使用带患者数据的方法
            return syncPatientByTypeAsyncInternal(hospitalId, patientType, patientList, taskId);
        } catch (Exception e) {
            log.error("准备患者数据时发生异常: {}", e.getMessage(), e);
            
            // 创建失败结果并直接返回，避免递归调用
            SyncResultDTO result = SyncResultDTO.builder()
                    .status(SyncConstants.SyncStatus.FAILED)
                    .message(SyncConstants.SyncMessage.FAILED)
                    .errorMessage("准备患者数据时发生异常: " + e.getMessage())
                    .hospitalId(hospitalId)
                    .tableNameOrPatientType("patient:" + patientType)
                    .startTime(LocalDateTime.now())
                    .endTime(LocalDateTime.now())
                    .build();
            
            return CompletableFuture.completedFuture(result);
        }
    }
    
    @Override
    public ResponseResult<SyncResultDTO> getSyncStatus(String hospitalId, String tableName) {
        log.info("获取医院[{}]的[{}]数据同步状态", hospitalId, tableName);
        
        SyncResultDTO result = getSyncStatusFromRedis(hospitalId, tableName);
        if (result == null) {
            result = SyncResultDTO.builder()
                    .status(SyncConstants.SyncStatus.SUCCESS)
                    .message("没有同步记录")
                    .hospitalId(hospitalId)
                    .tableNameOrPatientType(tableName)
                    .build();
        }
        
        return ResponseResult.success(result);
    }
    
    /**
     * 患者分类异步同步（带数据）
     * 立即返回任务ID，不阻塞主线程，任务在后台异步执行
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @param patients 患者数据列表（符合数据视图结构说明）
     * @return 同步结果，包含任务ID和初始状态
     */
    @Override
    public ResponseResult<SyncResultDTO> syncPatientByTypeAsync(String hospitalId, String patientType, List<Patient> patients) {
        log.info("启动异步患者分类同步任务，医院ID: {}，患者类型: {}, 数据量: {}", 
                hospitalId, patientType, (patients != null ? patients.size() : 0));
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的患者类型: " + patientType);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 检查是否有正在运行的任务
        if (asyncTaskManager.hasTask(taskKey)) {
            log.info("医院[{}]的[{}]类型患者同步任务正在进行中", hospitalId, patientType);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, "patient:" + patientType);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType("patient:" + patientType)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 生成唯一任务ID
        String taskId = UUID.randomUUID().toString();
        log.info("生成任务ID: {}", taskId);
        
        // 创建初始状态结果
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .taskId(taskId)
                .build();
        
        // 保存初始状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, initialResult);
        saveTaskStatusToRedis(taskId, initialResult);
        
        // 使用CompletableFuture.supplyAsync确保真正的异步执行
        CompletableFuture<SyncResultDTO> future = CompletableFuture.supplyAsync(() -> {
            try {
                log.info("异步任务开始执行，任务ID: {}, 线程: {}", taskId, Thread.currentThread().getName());
                return executePatientSyncTask(hospitalId, patientType, patients, taskId);
            } catch (Exception e) {
                log.error("异步任务执行异常，任务ID: {}, 异常: {}", taskId, e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }, syncTaskExecutor); // 使用指定的线程池
        
        // 注册任务到任务管理器，但不等待完成
        asyncTaskManager.registerTask(taskKey, future);
        
        // 添加任务完成后的状态更新回调
        future.whenComplete((result, ex) -> {
            try {
                if (ex != null) {
                    log.error("患者分类同步任务执行异常，任务ID: {}, 异常: {}", taskId, ex.getMessage(), ex);
                    // 更新任务状态为失败
                    SyncResultDTO failedResult = SyncResultDTO.builder()
                            .status(SyncConstants.SyncStatus.FAILED)
                            .message(SyncConstants.SyncMessage.FAILED)
                            .errorMessage(ex.getMessage())
                            .hospitalId(hospitalId)
                            .tableNameOrPatientType("patient:" + patientType)
                            .startTime(initialResult.getStartTime())
                            .endTime(LocalDateTime.now())
                            .taskId(taskId)
                            .build();
                    if (failedResult.getStartTime() != null) {
                        failedResult.setCostTime(Duration.between(failedResult.getStartTime(), failedResult.getEndTime()).toMillis());
                    }
                    
                    saveSyncStatusToRedis(hospitalId, "patient:" + patientType, failedResult);
                    saveTaskStatusToRedis(taskId, failedResult);
                } else {
                    log.info("患者分类同步任务执行完成，任务ID: {}, 状态: {}, 线程: {}", taskId, result.getStatus(), Thread.currentThread().getName());
                    // 确保任务成功完成后状态已更新，防止任务一直显示"同步中"
                    if (result.getStatus() == null || result.getStatus() == SyncConstants.SyncStatus.IN_PROGRESS) {
                        log.info("任务完成但状态未更新，手动更新状态为成功，任务ID: {}", taskId);
                        result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                        result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                        result.setEndTime(LocalDateTime.now());
                        if (result.getStartTime() != null) {
                            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                        }
                        
                        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
                        saveTaskStatusToRedis(taskId, result);
                    }
                }
            } catch (Exception e) {
                log.error("更新任务完成状态时发生异常: {}", e.getMessage(), e);
            }
        });
        
        log.info("患者分类同步任务已启动，立即返回任务ID: {}", taskId);
        return ResponseResult.success(initialResult);
    }
    
    /**
     * 患者分类同步
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @return 同步结果，包含同步状态、同步数据量、耗时等信息
     */
    @Override
    public ResponseResult<SyncResultDTO> syncPatientByType(String hospitalId, String patientType) {
        log.info("开始同步医院[{}]的[{}]类型患者数据", hospitalId, patientType);
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的患者类型: " + patientType);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 检查是否有正在运行的任务
        if (asyncTaskManager.hasTask(taskKey)) {
            log.info("医院[{}]的[{}]类型患者同步任务正在进行中", hospitalId, patientType);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, "patient:" + patientType);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType("patient:" + patientType)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 生成唯一任务ID
        String taskId = UUID.randomUUID().toString();
        log.info("生成任务ID: {}", taskId);
        
        // 异步执行患者分类同步任务
        CompletableFuture<SyncResultDTO> future = syncPatientByTypeAsync(hospitalId, patientType);
        // 注册任务到任务管理器
        asyncTaskManager.registerTask(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .taskId(taskId)  // 添加任务ID
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, initialResult);
        
        // 同时保存任务状态到Redis，使用任务ID作为键
        saveTaskStatusToRedis(taskId, initialResult);
                
        return ResponseResult.success(initialResult);
    }

    @Override
    public ResponseResult<String> getLastSyncTime(String hospitalId, String tableName) {
        log.info("获取医院[{}]的[{}]数据最后同步时间", hospitalId, tableName);
        
        LocalDateTime lastSyncTime = getLastSyncTimeFromRedis(hospitalId, tableName);
        if (lastSyncTime == null) {
            return ResponseResult.success("未找到最后同步时间记录");
        }
        
        // 使用 try-catch 包装格式化操作，避免格式化异常
        try {
            return ResponseResult.success(lastSyncTime.format(DATE_TIME_FORMATTER));
        } catch (Exception e) {
            log.warn("格式化最后同步时间时出错: {}", e.getMessage());
            // 使用 ISO 标准格式作为备用
            return ResponseResult.success(lastSyncTime.toString());
        }
    }
    
    @Override
    public ResponseResult<String> clearTaskStatus(String hospitalId, String tableNameOrType, String syncType) {
        log.info("清理任务状态 - 医院ID: {}, 表名或患者类型: {}, 同步类型: {}", hospitalId, tableNameOrType, syncType);
        
        String taskKey = buildTaskKey(hospitalId, tableNameOrType, syncType);
        boolean removed = false;
        
        if (asyncTaskManager.hasTask(taskKey)) {
            asyncTaskManager.removeTask(taskKey);
            removed = true;
            log.info("已清理任务状态: {}", taskKey);
        }
        
        // 清理Redis中的同步状态
        try {
            String redisKey = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableNameOrType);
            if (redisUtils.hasKey(redisKey)) {
                redisUtils.delete(redisKey);
                removed = true;
                log.info("已清理Redis中的同步状态: {}", redisKey);
            }
        } catch (Exception e) {
            log.error("清理Redis中的同步状态时发生异常: {}", e.getMessage(), e);
        }
        
        if (removed) {
            return ResponseResult.success("任务状态清理成功: " + taskKey);
        } else {
            return ResponseResult.success("未找到需要清理的任务状态: " + taskKey);
        }
    }
    
    @Override
    public ResponseResult<String> clearAllTaskStatus() {
        log.info("清理所有任务状态");
        
        int count = asyncTaskManager.getTaskCount();
        asyncTaskManager.clearTasks();
        asyncTaskManager.clearRetryCounts();
        
        log.info("已清理{}个任务状态", count);
        
        return ResponseResult.success("已清理所有任务状态，共" + count + "个");
    }
    
    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @Override
    public ResponseResult<SyncResultDTO> getTaskStatus(String taskId) {
        log.info("获取任务状态，任务ID: {}", taskId);
        
        if (taskId == null || taskId.trim().isEmpty()) {
            log.error("任务ID为空");
            return ResponseResult.error("任务ID不能为空");
        }
        
        // 从Redis中获取任务状态
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_TASK_STATUS, taskId);
        Object obj = redisUtils.get(key);
        
        if (obj != null) {
            try {
                SyncResultDTO result = JSON.parseObject(obj.toString(), SyncResultDTO.class);

                // 记录详细的任务状态信息
                log.info("任务[{}]状态详情: 状态={}, 消息={}, 总数据量={}, 成功数量={}, 失败数量={}",
                        taskId, result.getStatus(), result.getMessage(),
                        result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

                // 记录处理统计信息
                if (result.getTotalCount() != null && result.getTotalCount() > 0) {
                    log.info("任务[{}]处理统计: 总处理={}, 成功={}, 更新={}, 插入={}, 跳过={}, 失败={}",
                            taskId, result.getTotalCount(), result.getSuccessCount(),
                            result.getUpdatedCount(), result.getInsertedCount(),
                            result.getSkippedCount(), result.getFailedCount());
                }

                // 记录分类统计信息（患者数据）
                if (result.getClassificationStats() != null && !result.getClassificationStats().isEmpty()) {
                    log.info("任务[{}]分类统计: {}", taskId, result.getClassificationStats());
                }

                // 记录性能统计信息
                if (result.getPerformanceStats() != null && !result.getPerformanceStats().isEmpty()) {
                    log.info("任务[{}]性能统计: {}", taskId, result.getPerformanceStats());
                }

                // 如果有更新记录，记录日志
                if (result.getUpdatedRecords() != null && !result.getUpdatedRecords().isEmpty()) {
                    log.info("任务[{}]更新记录数: {}", taskId, result.getUpdatedRecords().size());
                }

                return ResponseResult.success(result);
            } catch (Exception e) {
                log.error("解析任务状态时发生异常: {}", e.getMessage(), e);
                return ResponseResult.error("解析任务状态时发生异常: " + e.getMessage());
            }
        } else {
            log.warn("未找到任务ID为{}的状态信息", taskId);
            return ResponseResult.error("任务不存在或已过期");
        }
    }
    
    /**
     * 清除Redis中的原有数据
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     */
    private void clearExistingDataFromRedis(String hospitalId, String tableName) {
        String redisKey;

        switch (tableName) {
            case SyncConstants.TableName.DEPARTMENT:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_DEPARTMENT + hospitalId;
                break;
            case SyncConstants.TableName.USER:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_USER + hospitalId;
                break;
            case SyncConstants.TableName.PATIENT:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT + hospitalId;
                break;
            default:
                log.error("未知的表名: {}", tableName);
                throw new BusinessException(ResultCode.PARAM_ERROR, "未知的表名: " + tableName);
        }

        // 删除Redis中的原有数据
        boolean deleted = redisUtils.delete(redisKey);
        log.info("清除医院[{}]的[{}]数据，Redis键: {}，删除结果: {}", hospitalId, tableName, redisKey, deleted);
    }

    /**
     * 将数据存入Redis（使用Hash结构，与患者数据同步保持一致）
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @param dataList 数据列表
     */
    private void saveDataToRedis(String hospitalId, String tableName, List<?> dataList) {
        String redisKey;

        switch (tableName) {
            case SyncConstants.TableName.DEPARTMENT:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_DEPARTMENT + hospitalId;
                break;
            case SyncConstants.TableName.USER:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_USER + hospitalId;
                break;
            case SyncConstants.TableName.PATIENT:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT + hospitalId;
                break;
            default:
                log.error("未知的表名: {}", tableName);
                throw new BusinessException(ResultCode.PARAM_ERROR, "未知的表名: " + tableName);
        }

        // 先清除原有数据（全量同步需要完全替换）
        log.info("开始清除医院[{}]的[{}]原有数据", hospitalId, tableName);
        boolean deleted = redisUtils.delete(redisKey);
        log.info("清除医院[{}]的[{}]数据，Redis键: {}，删除结果: {}", hospitalId, tableName, redisKey, deleted);

        if (dataList == null || dataList.isEmpty()) {
            log.warn("数据列表为空，跳过Redis存储");
            return;
        }

        try {
            // 将数据列表转换为Hash结构
            Map<String, Object> dataMap = new HashMap<>();

            for (int i = 0; i < dataList.size(); i++) {
                Object item = dataList.get(i);
                String itemKey = generateDataKey(tableName, item, i);
                String itemJson = JSON.toJSONString(item);
                dataMap.put(itemKey, itemJson);
            }

            log.info("开始保存医院[{}]的[{}]数据到Redis Hash结构，键: {}，数据量: {}",
                    hospitalId, tableName, redisKey, dataMap.size());

            // 使用Hash结构批量存储数据
            if (dataMap.size() > 1000) {
                // 大量数据使用管道批量写入
                redisUtils.hSetAllPipeline(redisKey, dataMap);
                log.info("使用管道批量写入大量数据到Redis Hash结构");
            } else {
                // 少量数据使用普通批量写入
                redisUtils.hSetAll(redisKey, dataMap);
                log.info("使用普通批量写入数据到Redis Hash结构");
            }

            // 设置过期时间
            redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);

            log.info("保存医院[{}]的[{}]数据到Redis Hash结构完成，键: {}，数据量: {}",
                    hospitalId, tableName, redisKey, dataMap.size());

        } catch (Exception e) {
            log.error("保存医院[{}]的[{}]数据到Redis时发生异常: {}", hospitalId, tableName, e.getMessage(), e);
            throw new BusinessException(ResultCode.PARAM_ERROR, "数据保存失败: " + e.getMessage());
        }
    }

    /**
     * 生成数据项的键
     *
     * @param tableName 表名
     * @param item 数据项
     * @param index 索引
     * @return 数据键
     */
    private String generateDataKey(String tableName, Object item, int index) {
        try {
            // 尝试从数据项中提取主键
            if (item instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) item;
                switch (tableName) {
                    case SyncConstants.TableName.DEPARTMENT:
                        Object deptId = map.get("departmentId");
                        if (deptId != null) {
                            return "dept_" + deptId.toString();
                        }
                        break;
                    case SyncConstants.TableName.USER:
                        Object userName = map.get("userName");
                        if (userName != null) {
                            return "user_" + userName.toString();
                        }
                        break;
                    case SyncConstants.TableName.PATIENT:
                        Object patientId = map.get("inpatientInfoId");
                        if (patientId != null) {
                            return "patient_" + patientId.toString();
                        }
                        break;
                }
            } else {
                // 使用反射获取主键字段
                Field[] fields = item.getClass().getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();

                    // 根据表名匹配主键字段
                    boolean isPrimaryKey = false;
                    switch (tableName) {
                        case SyncConstants.TableName.DEPARTMENT:
                            isPrimaryKey = "departmentId".equals(fieldName);
                            break;
                        case SyncConstants.TableName.USER:
                            isPrimaryKey = "userName".equals(fieldName);
                            break;
                        case SyncConstants.TableName.PATIENT:
                            isPrimaryKey = "inpatientInfoId".equals(fieldName);
                            break;
                    }

                    if (isPrimaryKey) {
                        Object value = field.get(item);
                        if (value != null) {
                            return tableName + "_" + value.toString();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("无法从数据项中提取主键，使用索引作为键: {}", e.getMessage());
        }

        // 如果无法提取主键，使用表名+索引作为键
        return tableName + "_" + index;
    }
    
    /**
     * 模拟获取数据（在实际应用中会从数据库或外部系统获取）
     *
     * @param tableName 表名
     * @return 数据列表
     */
    private List<?> mockFetchData(String tableName) {
        // 模拟数据获取延迟
        try {
            // 随机延迟1-3秒，模拟网络延迟或数据库查询耗时
            Thread.sleep((long) (Math.random() * 2000) + 1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        switch (tableName) {
            case SyncConstants.TableName.DEPARTMENT:
                return mockDepartmentList();
            case SyncConstants.TableName.USER:
                return mockUserList();
            case SyncConstants.TableName.PATIENT:
                return mockPatientList();
            default:
                return new ArrayList<>();
        }
    }
    
    /**
     * 模拟获取增量数据（在实际应用中会从数据库或外部系统获取）
     *
     * @param tableName 表名
     * @param lastSyncTime 最后同步时间
     * @return 增量数据列表
     */
    private List<?> mockFetchIncrementalData(String tableName, LocalDateTime lastSyncTime) {
        // 模拟数据获取延迟
        try {
            // 随机延迟0.5-1.5秒，增量同步通常比全量同步快
            Thread.sleep((long) (Math.random() * 1000) + 500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 在实际应用中，会根据lastSyncTime过滤数据
        // 这里简化实现，随机返回部分数据作为增量
        List<?> allData = mockFetchData(tableName);
        List<Object> incrementalData = new ArrayList<>();
        
        // 随机选择30%的数据作为增量数据
        for (Object item : allData) {
            if (Math.random() < 0.3) {
                incrementalData.add(item);
            }
        }
        
        return incrementalData;
    }
    
    /**
     * 模拟获取指定类型的患者数据
     *
     * @param patientType 患者类型
     * @return 患者列表
     */
    private List<Patient> mockFetchPatientByType(String patientType) {
        // 模拟数据获取延迟
        try {
            Thread.sleep((long) (Math.random() * 1000) + 500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        List<Patient> allPatients = mockPatientList();
        List<Patient> filteredPatients = new ArrayList<>();
        
        // 如果是自动分类模式，直接返回所有患者数据，后续会根据患者状态进行分类
        if (SyncConstants.PatientType.AUTO.equals(patientType)) {
            return allPatients;
        }
        
        // 根据患者类型过滤
        for (Patient patient : allPatients) {
            boolean match = false;
            
            try {
                if (SyncConstants.PatientType.IN.equals(patientType)) {
                    // 入院患者：有入院时间，没有出院时间
                    match = patient.getInhospitalTime() != null && patient.getOuthospitalTime() == null;
                } else if (SyncConstants.PatientType.UP.equals(patientType)) {
                    // 在院患者：状态为在院（1）
                    match = patient.getStatus() != null && patient.getStatus() == 1;
                } else if (SyncConstants.PatientType.OUT.equals(patientType)) {
                    // 出院患者：状态为出院（2）
                    match = patient.getStatus() != null && patient.getStatus() == 2;
                }
            } catch (Exception e) {
                // 处理异常场景，例如数据缺失或格式问题
                log.error("处理患者数据时发生异常", e);
                // 在实际应用中，可能需要记录错误数据并继续处理其他数据
                continue;
            }
            
            if (match) {
                filteredPatients.add(patient);
            }
        }
        
        return filteredPatients;
    }
    
    /**
     * 模拟科室列表
     */
    private List<Department> mockDepartmentList() {
        List<Department> departments = new ArrayList<>();
        
        // 模拟科室数据
        Department dept1 = new Department();
        dept1.setDepartmentId(1);
        dept1.setCode("NEU");
        dept1.setName("神经科");
        dept1.setUpdatedAt(LocalDateTime.now());
        departments.add(dept1);
        
        Department dept2 = new Department();
        dept2.setDepartmentId(2);
        dept2.setCode("CAR");
        dept2.setName("心血管科");
        dept2.setUpdatedAt(LocalDateTime.now().minusDays(1));
        departments.add(dept2);
        
        Department dept3 = new Department();
        dept3.setDepartmentId(3);
        dept3.setCode("PED");
        dept3.setName("儿科");
        dept3.setUpdatedAt(LocalDateTime.now().minusDays(2));
        departments.add(dept3);
        
        Department dept4 = new Department();
        dept4.setDepartmentId(4);
        dept4.setCode("ORT");
        dept4.setName("骨科");
        dept4.setUpdatedAt(LocalDateTime.now().minusHours(12));
        departments.add(dept4);
        
        Department dept5 = new Department();
        dept5.setDepartmentId(5);
        dept5.setCode("DER");
        dept5.setName("皮肤科");
        dept5.setUpdatedAt(LocalDateTime.now().minusHours(6));
        departments.add(dept5);
        
        return departments;
    }
    
    /**
     * 模拟医护人员列表
     */
    private List<User> mockUserList() {
        List<User> users = new ArrayList<>();
        
        // 模拟医护人员数据
        User user1 = new User();
        user1.setUserName("D001");
        user1.setName("张三");
        user1.setSex(1);
        user1.setRoleId(1);  // 医生
        user1.setDeptId(1);
        user1.setInpatientWard("1病区");
        user1.setMobile("13800138001");
        user1.setUpdatedAt(LocalDateTime.now());
        users.add(user1);
        
        User user2 = new User();
        user2.setUserName("N001");
        user2.setName("李四");
        user2.setSex(2);
        user2.setRoleId(2);  // 护士
        user2.setDeptId(1);
        user2.setInpatientWard("1病区,2病区");
        user2.setMobile("13800138002");
        user2.setUpdatedAt(LocalDateTime.now().minusHours(2));
        users.add(user2);
        
        User user3 = new User();
        user3.setUserName("D002");
        user3.setName("王五");
        user3.setSex(1);
        user3.setRoleId(1);  // 医生
        user3.setDeptId(2);
        user3.setInpatientWard("3病区");
        user3.setMobile("13800138003");
        user3.setUpdatedAt(LocalDateTime.now().minusDays(1));
        users.add(user3);
        
        User user4 = new User();
        user4.setUserName("N002");
        user4.setName("赵六");
        user4.setSex(2);
        user4.setRoleId(2);  // 护士
        user4.setDeptId(2);
        user4.setInpatientWard("3病区,4病区");
        user4.setMobile("13800138004");
        user4.setUpdatedAt(LocalDateTime.now().minusDays(2));
        users.add(user4);
        
        User user5 = new User();
        user5.setUserName("D003");
        user5.setName("钱七");
        user5.setSex(1);
        user5.setRoleId(1);  // 医生
        user5.setDeptId(3);
        user5.setInpatientWard("5病区");
        user5.setMobile("13800138005");
        user5.setUpdatedAt(LocalDateTime.now().minusHours(12));
        users.add(user5);
        
        return users;
    }
    
    /**
     * 模拟患者列表
     */
    private List<Patient> mockPatientList() {
        List<Patient> patients = new ArrayList<>();
        
        // 患者1：在院患者（入院未出院）
        Patient patient1 = new Patient();
        patient1.setInpatientInfoId("P001");
        patient1.setName("孙八");
        patient1.setIdCard("110101199001011234");
        patient1.setMobile("13800138006");
        patient1.setSex(1);
        patient1.setAge(30);
        patient1.setBirthday(LocalDate.of(1990, 1, 1));
        patient1.setHospitalizationNo("H001");
        patient1.setInhospitalDiagnose("高血压");
        patient1.setDeptId(2);  // 心血管科
        patient1.setSickbedNo("2-101");
        patient1.setDoctorId("D002");
        patient1.setNurseId("N002");
        patient1.setNurseLevel(2);
        patient1.setInhospitalTime(LocalDateTime.now().minusDays(5));
        patient1.setOuthospitalTime(null);  // 未出院
        patient1.setStatus(1);  // 在院
        patient1.setCategory("医保");
        patient1.setInpatientWard("3病区");
        patient1.setUpdatedAt(LocalDateTime.now().minusHours(2));
        patients.add(patient1);
        
        // 患者2：出院患者
        Patient patient2 = new Patient();
        patient2.setInpatientInfoId("P002");
        patient2.setName("周九");
        patient2.setIdCard("110101199101022345");
        patient2.setMobile("13800138007");
        patient2.setSex(2);
        patient2.setAge(29);
        patient2.setBirthday(LocalDate.of(1991, 1, 2));
        patient2.setHospitalizationNo("H002");
        patient2.setInhospitalDiagnose("头痛");
        patient2.setDeptId(1);  // 神经科
        patient2.setSickbedNo("1-102");
        patient2.setDoctorId("D001");
        patient2.setNurseId("N001");
        patient2.setNurseLevel(3);
        patient2.setInhospitalTime(LocalDateTime.now().minusDays(10));
        patient2.setOuthospitalTime(LocalDateTime.now().minusDays(2));  // 已出院
        patient2.setStatus(2);  // 出院
        patient2.setCategory("自费");
        patient2.setInpatientWard("1病区");
        patient2.setUpdatedAt(LocalDateTime.now().minusDays(2));
        patients.add(patient2);
        
        // 患者3：入院患者（刚入院）
        Patient patient3 = new Patient();
        patient3.setInpatientInfoId("P003");
        patient3.setName("吴十");
        patient3.setIdCard("110101199202033456");
        patient3.setMobile("13800138008");
        patient3.setSex(1);
        patient3.setAge(28);
        patient3.setBirthday(LocalDate.of(1992, 2, 3));
        patient3.setHospitalizationNo("H003");
        patient3.setInhospitalDiagnose("肺炎");
        patient3.setDeptId(4);  // 呼吸科
        patient3.setSickbedNo("4-103");
        patient3.setDoctorId("D003");
        patient3.setNurseId("N002");
        patient3.setNurseLevel(2);
        patient3.setInhospitalTime(LocalDateTime.now().minusHours(6));  // 刚入院
        patient3.setOuthospitalTime(null);  // 未出院
        patient3.setStatus(1);  // 在院
        patient3.setCategory("医保");
        patient3.setInpatientWard("5病区");
        patient3.setUpdatedAt(LocalDateTime.now().minusHours(6));
        patients.add(patient3);
        
        // 患者4：异常情况 - 状态与出院时间不一致（数据异常）
        Patient patient4 = new Patient();
        patient4.setInpatientInfoId("P004");
        patient4.setName("郑十一");
        patient4.setIdCard("110101199303044567");
        patient4.setMobile("13800138009");
        patient4.setSex(2);
        patient4.setAge(27);
        patient4.setBirthday(LocalDate.of(1993, 3, 4));
        patient4.setHospitalizationNo("H004");
        patient4.setInhospitalDiagnose("骨折");
        patient4.setDeptId(3);  // 骨科
        patient4.setSickbedNo("3-104");
        patient4.setDoctorId("D002");
        patient4.setNurseId("N001");
        patient4.setNurseLevel(1);
        patient4.setInhospitalTime(LocalDateTime.now().minusDays(8));
        patient4.setOuthospitalTime(LocalDateTime.now().minusDays(1));  // 有出院时间
        patient4.setStatus(1);  // 但状态是在院（不一致）
        patient4.setCategory("自费");
        patient4.setInpatientWard("3病区");
        patient4.setUpdatedAt(LocalDateTime.now().minusDays(1));
        patients.add(patient4);
        
        // 患者5：异常情况 - 出院时间早于入院时间
        Patient patient5 = new Patient();
        patient5.setInpatientInfoId("P005");
        patient5.setName("王十二");
        patient5.setIdCard("110101199404055678");
        patient5.setMobile("13800138010");
        patient5.setSex(1);
        patient5.setAge(26);
        patient5.setBirthday(LocalDate.of(1994, 4, 5));
        patient5.setHospitalizationNo("H005");
        patient5.setInhospitalDiagnose("胃炎");
        patient5.setDeptId(5);  // 消化科
        patient5.setSickbedNo("5-105");
        patient5.setDoctorId("D003");
        patient5.setNurseId("N002");
        patient5.setNurseLevel(2);
        patient5.setInhospitalTime(LocalDateTime.now().minusDays(3));
        patient5.setOuthospitalTime(LocalDateTime.now().minusDays(4));  // 出院时间早于入院时间
        patient5.setStatus(2);  // 出院
        patient5.setCategory("医保");
        patient5.setInpatientWard("5病区");
        patient5.setUpdatedAt(LocalDateTime.now().minusDays(3));
        patients.add(patient5);
        
        return patients;
    }

    /**
     * 构建任务键
     */
    private String buildTaskKey(String hospitalId, String tableNameOrType, String syncType) {
        return hospitalId + ":" + tableNameOrType + ":" + syncType;
    }
    
    /**
     * 从Redis获取同步状态
     */
    private SyncResultDTO getSyncStatusFromRedis(String hospitalId, String tableName) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName);
        Object obj = redisUtils.get(key);
        if (obj != null) {
            SyncResultDTO result = JSON.parseObject(obj.toString(), SyncResultDTO.class);
            
            // 检查是否是"幽灵任务"（Redis中存在但内存中不存在）
            String taskKey = buildTaskKey(hospitalId, 
                                         tableName.startsWith("patient:") ? tableName.substring(8) : tableName, 
                                         tableName.startsWith("patient:") ? "patient" : "full");
            
            // 如果Redis中的状态是"进行中"，但内存中没有对应的任务，则认为是幽灵任务
            if (result.getStatus() == SyncConstants.SyncStatus.IN_PROGRESS && !asyncTaskManager.hasTask(taskKey)) {
                log.warn("检测到幽灵任务: {}, Redis状态为'进行中'但内存中不存在此任务，将自动清理", taskKey);
                
                // 更新状态为失败
                result.setStatus(SyncConstants.SyncStatus.FAILED);
                result.setMessage("任务异常终止");
                result.setEndTime(LocalDateTime.now());
                if (result.getStartTime() != null) {
                    result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                }
                
                // 保存更新后的状态到Redis
                saveSyncStatusToRedis(hospitalId, tableName, result);
                
                log.info("已清理幽灵任务状态: {}", taskKey);
            }
            
            return result;
        }
        return null;
    }
    
    /**
     * 保存同步状态到Redis
     */
    private void saveSyncStatusToRedis(String hospitalId, String tableName, SyncResultDTO result) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName);
        redisUtils.set(key, JSON.toJSONString(result), SyncConstants.ExpireTime.SYNC_STATUS);
    }
    
    /**
     * 保存最后同步时间到Redis
     */
    private void saveLastSyncTimeToRedis(String hospitalId, String tableName, LocalDateTime time) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName);
        long expireTime = SyncConstants.ExpireTime.LAST_SYNC_TIME;
        String timeStr = time.format(DATE_TIME_FORMATTER);
        
        // 处理过期时间为0的情况（永不过期）
        if (expireTime <= 0) {
            // 使用不带过期时间的set方法
            redisUtils.set(key, timeStr);
            log.debug("保存最后同步时间到Redis（永不过期）: {}", key);
        } else {
            // 确保过期时间是正整数
            redisUtils.set(key, timeStr, Math.max(1, expireTime));
            log.debug("保存最后同步时间到Redis（过期时间: {}秒）: {}", expireTime, key);
        }
        
        // 验证数据是否成功存储（仅在DEBUG级别输出）
        if (log.isDebugEnabled() && redisUtils.hasKey(key)) {
            Object storedValue = redisUtils.get(key);
            log.debug("验证Redis存储结果: 键={}, 值={}", key, storedValue);
        }
    }
    
    /**
     * 从Redis获取最后同步时间
     */
    private LocalDateTime getLastSyncTimeFromRedis(String hospitalId, String tableName) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName);
        Object obj = redisUtils.get(key);
        if (obj != null) {
            return LocalDateTime.parse(obj.toString(), DATE_TIME_FORMATTER);
        }
        return null;
    }

    /**
     * 测试直接将患者数据存入Redis Hash结构
     * 
     * @param hospitalId 医院ID
     * @param patientType 患者类型
     * @param patient 患者数据
     * @return 存储结果
     */
    public boolean testSavePatientToRedisHash(String hospitalId, String patientType, Patient patient) {
        if (patient == null || patient.getInpatientInfoId() == null) {
            log.error("患者数据为空或缺少inpatientInfoId");
            return false;
        }
        
        try {
            // 构建Redis键（使用新格式标准）
            String redisKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(patientType) + hospitalId;
            log.info("测试存储患者数据到Redis，键: {}, 患者ID: {}", redisKey, patient.getInpatientInfoId());

            // 生成患者唯一标识（作为Hash字段名）
            Long inhospitalTimeStamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
            String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
                patient.getHospitalizationNo(), inhospitalTimeStamp);

            // 将患者对象转换为标准格式的Map，然后转换为JSON字符串
            Map<String, Object> patientData = convertPatientToStorageFormat(patient);
            String patientJson = JSON.toJSONString(patientData, Feature.WriteNulls, Feature.WriteNullStringAsEmpty);
            log.info("患者JSON数据: {}", patientJson);

            // 使用Hash结构存储患者数据（使用新的字段名格式）
            redisUtils.hSet(redisKey, patientKey, patientJson);
            log.info("患者数据已存入Redis Hash结构");
            
            // 设置过期时间
            redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);
            
            // 验证数据是否成功存储
            if (redisUtils.hasKey(redisKey)) {
                Object storedData = redisUtils.hGet(redisKey, patient.getInpatientInfoId());
                if (storedData != null) {
                    log.info("验证Redis存储成功: 键={}, 字段={}, 值长度={}", 
                            redisKey, patient.getInpatientInfoId(), storedData.toString().length());
                    return true;
                } else {
                    log.warn("验证Redis存储失败: 字段{}不存在", patient.getInpatientInfoId());
                }
            } else {
                log.warn("验证Redis存储失败: 键{}不存在", redisKey);
            }
            
            return false;
        } catch (Exception e) {
            log.error("测试存储患者数据到Redis失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 合并患者数据，保留新数据中的非null字段，其他字段使用现有数据
     * 使用直接的getter/setter方法代替反射，提高性能
     * 
     * @param existingPatient 现有患者数据
     * @param newPatient 新患者数据
     * @return 合并后的患者数据以及是否有字段更新的信息
     */
    private Map<String, Object> mergePatientData(Patient existingPatient, Patient newPatient) {
        Patient mergedPatient = new Patient();
        int updatedFieldCount = 0;
        
        // 复制现有患者的所有字段
        BeanUtils.copyProperties(existingPatient, mergedPatient);
        
        // 使用直接的getter/setter方法代替反射，提高性能
        
        // 处理inpatientInfoId字段
        if (newPatient.getInpatientInfoId() != null && !Objects.equals(newPatient.getInpatientInfoId(), existingPatient.getInpatientInfoId())) {
            mergedPatient.setInpatientInfoId(newPatient.getInpatientInfoId());
            updatedFieldCount++;
        }
        
        // 处理name字段
        if (newPatient.getName() != null && !Objects.equals(newPatient.getName(), existingPatient.getName())) {
            mergedPatient.setName(newPatient.getName());
            updatedFieldCount++;
        }
        
        // 处理idCard字段
        if (newPatient.getIdCard() != null && !Objects.equals(newPatient.getIdCard(), existingPatient.getIdCard())) {
            mergedPatient.setIdCard(newPatient.getIdCard());
            updatedFieldCount++;
        }
        
        // 处理mobile字段
        if (newPatient.getMobile() != null && !Objects.equals(newPatient.getMobile(), existingPatient.getMobile())) {
            mergedPatient.setMobile(newPatient.getMobile());
            updatedFieldCount++;
        }
        
        // 处理sex字段
        if (newPatient.getSex() != null && !Objects.equals(newPatient.getSex(), existingPatient.getSex())) {
            mergedPatient.setSex(newPatient.getSex());
            updatedFieldCount++;
        }
        
        // 处理age字段
        if (newPatient.getAge() != null && !Objects.equals(newPatient.getAge(), existingPatient.getAge())) {
            mergedPatient.setAge(newPatient.getAge());
            updatedFieldCount++;
        }
        
        // 处理birthday字段
        if (newPatient.getBirthday() != null && !Objects.equals(newPatient.getBirthday(), existingPatient.getBirthday())) {
            mergedPatient.setBirthday(newPatient.getBirthday());
            updatedFieldCount++;
        }
        
        // 处理hospitalizationNo字段
        if (newPatient.getHospitalizationNo() != null && !Objects.equals(newPatient.getHospitalizationNo(), existingPatient.getHospitalizationNo())) {
            mergedPatient.setHospitalizationNo(newPatient.getHospitalizationNo());
            updatedFieldCount++;
        }
        
        // 处理inhospitalDiagnose字段
        if (newPatient.getInhospitalDiagnose() != null && !Objects.equals(newPatient.getInhospitalDiagnose(), existingPatient.getInhospitalDiagnose())) {
            mergedPatient.setInhospitalDiagnose(newPatient.getInhospitalDiagnose());
            updatedFieldCount++;
        }
        
        // 处理deptId字段
        if (newPatient.getDeptId() != null && !Objects.equals(newPatient.getDeptId(), existingPatient.getDeptId())) {
            mergedPatient.setDeptId(newPatient.getDeptId());
            updatedFieldCount++;
        }
        
        // 处理sickbedNo字段
        if (newPatient.getSickbedNo() != null && !Objects.equals(newPatient.getSickbedNo(), existingPatient.getSickbedNo())) {
            mergedPatient.setSickbedNo(newPatient.getSickbedNo());
            updatedFieldCount++;
        }
        
        // 处理doctorId字段
        if (newPatient.getDoctorId() != null && !Objects.equals(newPatient.getDoctorId(), existingPatient.getDoctorId())) {
            mergedPatient.setDoctorId(newPatient.getDoctorId());
            updatedFieldCount++;
        }
        
        // 处理nurseId字段
        if (newPatient.getNurseId() != null && !Objects.equals(newPatient.getNurseId(), existingPatient.getNurseId())) {
            mergedPatient.setNurseId(newPatient.getNurseId());
            updatedFieldCount++;
        }
        
        // 处理nurseLevel字段
        if (newPatient.getNurseLevel() != null && !Objects.equals(newPatient.getNurseLevel(), existingPatient.getNurseLevel())) {
            mergedPatient.setNurseLevel(newPatient.getNurseLevel());
            updatedFieldCount++;
        }
        
        // 处理inhospitalTime字段
        if (newPatient.getInhospitalTime() != null && !Objects.equals(newPatient.getInhospitalTime(), existingPatient.getInhospitalTime())) {
            mergedPatient.setInhospitalTime(newPatient.getInhospitalTime());
            updatedFieldCount++;
        }
        
        // 处理outhospitalTime字段
        if (newPatient.getOuthospitalTime() != null && !Objects.equals(newPatient.getOuthospitalTime(), existingPatient.getOuthospitalTime())) {
            mergedPatient.setOuthospitalTime(newPatient.getOuthospitalTime());
            updatedFieldCount++;
        }
        
        // 处理status字段
        if (newPatient.getStatus() != null && !Objects.equals(newPatient.getStatus(), existingPatient.getStatus())) {
            mergedPatient.setStatus(newPatient.getStatus());
            updatedFieldCount++;
        }
        
        // 处理category字段
        if (newPatient.getCategory() != null && !Objects.equals(newPatient.getCategory(), existingPatient.getCategory())) {
            mergedPatient.setCategory(newPatient.getCategory());
            updatedFieldCount++;
        }
        
        // 处理inpatientWard字段
        if (newPatient.getInpatientWard() != null && !Objects.equals(newPatient.getInpatientWard(), existingPatient.getInpatientWard())) {
            mergedPatient.setInpatientWard(newPatient.getInpatientWard());
            updatedFieldCount++;
        }
        
        // 如果有字段更新，则更新时间戳为当前时间
        if (updatedFieldCount > 0) {
            mergedPatient.setUpdatedAt(LocalDateTime.now());
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("patient", mergedPatient);
        result.put("updatedFieldCount", updatedFieldCount);
        
        return result;
    }
    
    /**
     * 计算Patient对象中非null字段的数量
     * 
     * @param patient 患者对象
     * @return 非null字段数量
     */
    private int countNonNullFields(Patient patient) {
        if (patient == null) {
            return 0;
        }
        
        int count = 0;
        Field[] fields = Patient.class.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.get(patient) != null) {
                    count++;
                }
            } catch (IllegalAccessException e) {
                // 忽略访问异常
            }
        }
        
        return count;
    }
    
    /**
     * 生成患者的业务唯一键，用于数据去重
     * 只使用一种规则：住院号+入院时间 (hospitalizationNo+inhospitalTime)
     * 
     * @param patient 患者对象
     * @return 业务唯一键，如果无法生成则返回null
     */
    private String generatePatientBusinessKey(Patient patient) {
        if (patient == null) {
            return null;
        }
        
        // 只使用住院号+入院时间作为唯一标识，直接连接，不使用前缀和分隔符
        if (patient.getHospitalizationNo() != null && patient.getInhospitalTime() != null) {
            String formattedTime = patient.getInhospitalTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            return patient.getHospitalizationNo() + formattedTime;
        }
        
        // 如果不满足条件，返回null表示无法生成唯一标识
        return null;
    }
    
    /**
     * 验证Redis数据是否成功写入
     * 优化版本：仅在DEBUG日志级别启用时进行验证，减少生产环境的开销
     * 
     * @param redisKey Redis键
     * @param dataMap 要验证的数据Map
     * @return 验证是否成功
     */
    private boolean validateRedisData(String redisKey, Map<String, Object> dataMap) {
        // 只在DEBUG日志级别启用时进行验证，减少生产环境的开销
        if (!log.isDebugEnabled()) {
            return true; // 非调试模式下直接返回成功
        }
        
        if (dataMap == null || dataMap.isEmpty()) {
            log.warn("无数据需要验证");
            return false;
        }
        
        if (!redisUtils.hasKey(redisKey)) {
            log.error("Redis键不存在: {}", redisKey);
            return false;
        }
        
        // 减少抽样数量，只验证1个字段
        int sampleSize = Math.min(1, dataMap.size());
        List<String> sampleKeys = new ArrayList<>(dataMap.keySet());
        
        // 直接取第一个键，避免随机打乱的开销
        boolean exists = redisUtils.hExists(redisKey, sampleKeys.get(0));
        
        if (exists) {
            log.debug("Redis数据验证成功: 键={}", redisKey);
        } else {
            log.warn("Redis数据验证失败: 键={}, 字段={}", redisKey, sampleKeys.get(0));
        }
        
        return exists;
    }

    /**
     * 保存任务状态到Redis
     * 
     * @param taskId 任务ID
     * @param result 任务结果
     */
    private void saveTaskStatusToRedis(String taskId, SyncResultDTO result) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_TASK_STATUS, taskId);
        redisUtils.set(key, JSON.toJSONString(result), SyncConstants.ExpireTime.SYNC_STATUS);
    }

    /**
     * 执行患者分类同步任务（同步方法，用于CompletableFuture.supplyAsync调用）
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @param patients 患者数据列表
     * @param taskId 任务ID
     * @return 同步结果
     */
    private SyncResultDTO executePatientSyncTask(String hospitalId, String patientType, List<Patient> patients, String taskId) {
        // 记录开始时间，用于性能监控
        long startTime = System.currentTimeMillis();
        
        // 创建更新记录列表
        List<Map<String, Object>> updatedRecords = new ArrayList<>();
        
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .taskId(taskId) // 添加任务ID
                .build();
        
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
        // 保存任务状态到Redis
        saveTaskStatusToRedis(taskId, result);
        
        try {
            if (patients == null || patients.isEmpty()) {
                // 先计算耗时
                result.setEndTime(LocalDateTime.now());
                long costTime = Duration.between(result.getStartTime(), result.getEndTime()).toMillis();
                result.setCostTime(costTime);

                // 设置空数据的统计信息
                Map<String, Object> performanceStats = new HashMap<>();
                performanceStats.put("totalProcessingTime", costTime);
                performanceStats.put("averageProcessingTimePerRecord", 0);
                performanceStats.put("dataValidationTime", "N/A");
                performanceStats.put("redisWriteTime", "N/A");

                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.NO_DATA);
                result.setCount(0);
                result.setTotalCount(0);
                result.setSuccessCount(0);
                result.setUpdatedCount(0);
                result.setInsertedCount(0);
                result.setSkippedCount(0);
                result.setFailedCount(0);
                result.setPerformanceStats(performanceStats);

                log.info("医院[{}]的[{}]患者数据为空，同步完成", hospitalId, patientType);
            } else {
                // 记录合并开始时间
                long mergeStartTime = System.currentTimeMillis();

                // 使用增量更新模式存储患者数据
                log.info("准备使用增量更新模式存储{}个患者[{}]数据", patients.size(), patientType);
                int storedCount = storePatientDataWithIncrementalUpdate(hospitalId, patientType, patients);

                // 记录处理统计
                int processedCount = patients.size();
                int validCount = storedCount;
                int skippedCount = processedCount - validCount;

                // 先计算耗时
                result.setEndTime(LocalDateTime.now());
                long costTime = Duration.between(result.getStartTime(), result.getEndTime()).toMillis();
                result.setCostTime(costTime);

                // 收集性能统计信息
                Map<String, Object> performanceStats = new HashMap<>();
                performanceStats.put("totalProcessingTime", costTime);
                performanceStats.put("averageProcessingTimePerRecord", processedCount > 0 ? costTime / processedCount : 0);
                performanceStats.put("dataValidationTime", "N/A");
                performanceStats.put("redisWriteTime", "N/A");

                // 更新结果（包含详细统计信息）
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                result.setCount(validCount);
                result.setTotalCount(processedCount);
                result.setSuccessCount(validCount);
                result.setUpdatedCount(validCount); // 患者同步是增量更新
                result.setInsertedCount(0); // 增量同步主要是更新，不是插入
                result.setSkippedCount(skippedCount);
                result.setFailedCount(skippedCount);
                result.setPerformanceStats(performanceStats);
                result.setUpdatedRecords(updatedRecords);

                log.info("医院[{}]的[{}]患者同步完成，总处理: {}，成功: {}，更新: {}，跳过: {}，失败: {}，耗时: {}ms",
                        hospitalId, patientType, processedCount, validCount, validCount, skippedCount, skippedCount, result.getCostTime());
            }

            // 更新最后同步时间
            saveLastSyncTimeToRedis(hospitalId, "patient:" + patientType, result.getEndTime());

            // 保存结果到Redis
            saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
            saveTaskStatusToRedis(taskId, result);

            return result;
        } catch (Exception e) {
            log.error("执行患者分类同步任务时发生异常: {}", e.getMessage(), e);
            
            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            
            // 保存结果到Redis
            saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
            saveTaskStatusToRedis(taskId, result);
            
            return result;
        }
    }

    /**
     * 执行全量同步任务（完全参考患者数据同步方式）
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @param dataList 数据列表（可选）
     * @param taskId 任务ID
     * @return 同步结果
     */
    private SyncResultDTO executeFullSyncTask(String hospitalId, String tableName, List<Object> dataList, String taskId) {
        // 记录开始时间，用于性能监控
        long startTime = System.currentTimeMillis();

        // 创建更新记录列表
        List<Map<String, Object>> updatedRecords = new ArrayList<>();

        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .taskId(taskId)
                .build();

        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, result);
        saveTaskStatusToRedis(taskId, result);

        try {
            List<?> finalDataList;

            // 如果没有提供数据，则从外部系统获取
            if (dataList == null || dataList.isEmpty()) {
                log.info("未提供数据，从外部系统获取医院[{}]的[{}]数据", hospitalId, tableName);
                finalDataList = mockFetchData(tableName);
            } else {
                log.info("使用提供的数据，医院[{}]的[{}]数据量: {}", hospitalId, tableName, dataList.size());
                finalDataList = dataList;
            }

            if (finalDataList == null || finalDataList.isEmpty()) {
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.NO_DATA);
                result.setCount(0);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());

                log.info("医院[{}]的[{}]数据为空，全量同步完成", hospitalId, tableName);
            } else {
                // 根据表名采用不同的处理方式
                log.info("全量同步数据处理，tableName: {}, PATIENT常量: {}, 是否相等: {}",
                        tableName, SyncConstants.TableName.PATIENT, SyncConstants.TableName.PATIENT.equals(tableName));

                if (SyncConstants.TableName.PATIENT.equals(tableName)) {
                    // 患者数据：按照患者接口的auto方式分类处理
                    log.info("进入患者数据auto分类处理逻辑，医院ID: {}", hospitalId);
                    processPatientDataWithAutoClassification(hospitalId, finalDataList, result, updatedRecords);
                } else {
                    // 非患者数据：按照原有方式处理
                    log.info("进入非患者数据处理逻辑，医院ID: {}, 表名: {}", hospitalId, tableName);
                    processNonPatientData(hospitalId, tableName, finalDataList, result, updatedRecords);
                }
            }

            // 保存结果到Redis
            saveSyncStatusToRedis(hospitalId, tableName, result);
            saveTaskStatusToRedis(taskId, result);

            return result;
        } catch (Exception e) {
            log.error("执行全量同步任务时发生异常: {}", e.getMessage(), e);

            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());

            // 保存结果到Redis
            saveSyncStatusToRedis(hospitalId, tableName, result);
            saveTaskStatusToRedis(taskId, result);

            return result;
        }
    }

    /**
     * 构建Redis键（科室和医护保持原格式，患者使用新格式）
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @return Redis键
     */
    private String buildRedisKey(String hospitalId, String tableName) {
        switch (tableName) {
            case SyncConstants.TableName.DEPARTMENT:
                return SyncConstants.RedisKeyPrefix.HOSPITAL_DEPARTMENT + hospitalId;
            case SyncConstants.TableName.USER:
                return SyncConstants.RedisKeyPrefix.HOSPITAL_USER + hospitalId;
            case SyncConstants.TableName.PATIENT:
                // 患者数据使用auto分类处理，不会直接使用这个键
                return "Patient-" + hospitalId;
            default:
                log.error("未知的表名: {}", tableName);
                throw new BusinessException(ResultCode.PARAM_ERROR, "未知的表名: " + tableName);
        }
    }

    /**
     * 生成字段名（完全参考患者数据同步方式）
     *
     * @param tableName 表名
     * @param item 数据项
     * @param index 索引
     * @return 字段名
     */
    private String generateFieldName(String tableName, Object item, int index) {
        try {
            // 尝试从对象中提取唯一标识，参考患者数据的方式
            if (item instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) item;
                Object id = null;
                Object timeField = null;

                switch (tableName) {
                    case SyncConstants.TableName.DEPARTMENT:
                        id = map.get("departmentId");
                        if (id == null) id = map.get("code");
                        // 部门数据使用简单的ID作为字段名
                        if (id != null) {
                            return "dept_" + id;
                        }
                        break;
                    case SyncConstants.TableName.USER:
                        id = map.get("userName");
                        if (id == null) id = map.get("name");
                        // 用户数据使用简单的ID作为字段名
                        if (id != null) {
                            return "user_" + id;
                        }
                        break;
                    case SyncConstants.TableName.PATIENT:
                        // 患者数据使用住院号+时间的方式（参考患者接口）
                        Object hospNo = map.get("hospitalizationNo");
                        Object inhospitalTime = map.get("inhospitalTime");
                        if (hospNo != null && inhospitalTime != null) {
                            // 如果时间是字符串格式，需要转换
                            String formattedTime;
                            if (inhospitalTime instanceof String) {
                                try {
                                    LocalDateTime dateTime = LocalDateTime.parse(inhospitalTime.toString());
                                    formattedTime = dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                                } catch (Exception e) {
                                    formattedTime = inhospitalTime.toString().replaceAll("[^0-9]", "");
                                }
                            } else {
                                formattedTime = inhospitalTime.toString().replaceAll("[^0-9]", "");
                            }
                            return "hosptime_" + hospNo + "_" + formattedTime;
                        }
                        // 如果没有住院号和时间，使用患者ID
                        id = map.get("inpatientInfoId");
                        if (id != null) {
                            return "patient_" + id;
                        }
                        break;
                }
            }
        } catch (Exception e) {
            log.debug("提取唯一标识失败，使用索引: {}", e.getMessage());
        }

        // 如果无法提取唯一标识，使用索引
        return tableName + "_" + index;
    }

    /**
     * 处理患者数据，按照患者接口的auto方式分类（完全参考患者接口实现）
     *
     * @param hospitalId 医院ID
     * @param dataList 患者数据列表
     * @param result 同步结果
     * @param updatedRecords 更新记录列表
     */
    private void processPatientDataWithAutoClassification(String hospitalId, List<?> dataList, SyncResultDTO result, List<Map<String, Object>> updatedRecords) {
        log.info("开始按照患者接口auto方式处理患者数据，医院ID: {}，数据量: {}", hospitalId, dataList.size());

        // 将数据转换为Patient对象列表
        List<Patient> patients = new ArrayList<>();
        for (Object item : dataList) {
            try {
                Patient patient;
                if (item instanceof Patient) {
                    patient = (Patient) item;
                } else if (item instanceof Map) {
                    // 将Map转换为Patient对象
                    String json = JSON.toJSONString(item);
                    patient = JSON.parseObject(json, Patient.class);
                } else {
                    log.warn("无法处理的患者数据类型: {}", item.getClass().getName());
                    continue;
                }
                patients.add(patient);
            } catch (Exception e) {
                log.error("转换患者数据时发生异常: {}", e.getMessage(), e);
            }
        }

        if (patients.isEmpty()) {
            // 先计算耗时
            result.setEndTime(LocalDateTime.now());
            long costTime = Duration.between(result.getStartTime(), result.getEndTime()).toMillis();
            result.setCostTime(costTime);

            // 设置空数据的统计信息
            Map<String, Object> performanceStats = new HashMap<>();
            performanceStats.put("totalProcessingTime", costTime);
            performanceStats.put("averageProcessingTimePerRecord", 0);
            performanceStats.put("dataValidationTime", "N/A");
            performanceStats.put("redisWriteTime", "N/A");

            result.setStatus(SyncConstants.SyncStatus.SUCCESS);
            result.setMessage(SyncConstants.SyncMessage.NO_DATA);
            result.setCount(0);
            result.setTotalCount(0);
            result.setSuccessCount(0);
            result.setUpdatedCount(0);
            result.setInsertedCount(0);
            result.setSkippedCount(0);
            result.setFailedCount(0);
            result.setPerformanceStats(performanceStats);

            log.info("没有有效的患者数据需要处理");
            return;
        }

        // 按照患者状态进行auto分类
        Map<String, List<Patient>> classifiedPatients = new HashMap<>();
        classifiedPatients.put(SyncConstants.PatientType.IN, new ArrayList<>());
        classifiedPatients.put(SyncConstants.PatientType.UP, new ArrayList<>());
        classifiedPatients.put(SyncConstants.PatientType.OUT, new ArrayList<>());

        int totalProcessed = 0;
        int totalValid = 0;
        int totalSkipped = 0;

        for (Patient patient : patients) {
            totalProcessed++;

            // 检查患者数据的有效性（必须有住院号和入院时间）
            if (patient.getHospitalizationNo() == null || patient.getInhospitalTime() == null) {
                log.debug("患者数据缺少必要字段，跳过: ID={}, 住院号={}, 入院时间={}",
                        patient.getInpatientInfoId(), patient.getHospitalizationNo(), patient.getInhospitalTime());
                totalSkipped++;
                continue;
            }

            // 根据患者状态进行分类（完全参考患者接口逻辑）
            if (patient.getStatus() != null) {
                switch (patient.getStatus()) {
                    case 1:
                        // 在院患者：同时加入IN和UP分类
                        classifiedPatients.get(SyncConstants.PatientType.IN).add(patient);
                        classifiedPatients.get(SyncConstants.PatientType.UP).add(patient);
                        totalValid++;
                        break;
                    case 2:
                        // 出院患者：加入OUT分类
                        classifiedPatients.get(SyncConstants.PatientType.OUT).add(patient);
                        totalValid++;
                        break;
                    default:
                        log.debug("患者状态未知，跳过: ID={}, 状态={}", patient.getInpatientInfoId(), patient.getStatus());
                        totalSkipped++;
                        break;
                }
            } else {
                log.debug("患者状态为空，跳过: ID={}", patient.getInpatientInfoId());
                totalSkipped++;
            }
        }

        log.info("患者数据auto分类完成，总数: {}，有效: {}，跳过: {}，IN: {}，UP: {}，OUT: {}",
                totalProcessed, totalValid, totalSkipped,
                classifiedPatients.get(SyncConstants.PatientType.IN).size(),
                classifiedPatients.get(SyncConstants.PatientType.UP).size(),
                classifiedPatients.get(SyncConstants.PatientType.OUT).size());

        // 分别处理每个分类的患者数据
        int totalStoredCount = 0;
        Map<String, Integer> classificationStats = new HashMap<>();
        Map<String, Object> performanceStats = new HashMap<>();

        for (Map.Entry<String, List<Patient>> entry : classifiedPatients.entrySet()) {
            String patientType = entry.getKey();
            List<Patient> patientsOfType = entry.getValue();

            if (!patientsOfType.isEmpty()) {
                int storedCount = storePatientDataWithNewFormat(hospitalId, patientType, patientsOfType);
                totalStoredCount += storedCount;
                classificationStats.put(patientType, storedCount);
                log.info("患者类型[{}]数据存储完成，存储数量: {}", patientType, storedCount);
            } else {
                classificationStats.put(patientType, 0);
            }
        }

        // 先计算耗时
        result.setEndTime(LocalDateTime.now());
        long costTime = Duration.between(result.getStartTime(), result.getEndTime()).toMillis();
        result.setCostTime(costTime);

        // 收集性能统计信息
        performanceStats.put("totalProcessingTime", costTime);
        performanceStats.put("averageProcessingTimePerRecord", totalProcessed > 0 ? costTime / totalProcessed : 0);
        performanceStats.put("dataValidationTime", "N/A"); // 可以在后续版本中添加具体的时间统计
        performanceStats.put("redisWriteTime", "N/A");

        // 更新结果（包含详细统计信息）
        result.setStatus(SyncConstants.SyncStatus.SUCCESS);
        result.setMessage(SyncConstants.SyncMessage.SUCCESS);
        result.setCount(totalStoredCount);
        result.setTotalCount(totalProcessed);
        result.setSuccessCount(totalValid);
        result.setUpdatedCount(0); // 全量同步不是更新，而是替换
        result.setInsertedCount(totalStoredCount); // 全量同步都是新插入
        result.setSkippedCount(totalSkipped);
        result.setFailedCount(totalProcessed - totalValid);
        result.setClassificationStats(classificationStats);
        result.setPerformanceStats(performanceStats);
        result.setUpdatedRecords(updatedRecords);

        // 更新最后同步时间（为每个患者类型都更新）
        for (String patientType : classifiedPatients.keySet()) {
            saveLastSyncTimeToRedis(hospitalId, "patient:" + patientType, result.getEndTime());
        }

        log.info("医院[{}]的患者数据auto分类全量同步完成，总处理: {}，成功: {}，存储: {}，跳过: {}，失败: {}，耗时: {}ms",
                hospitalId, totalProcessed, totalValid, totalStoredCount, totalSkipped, (totalProcessed - totalValid), result.getCostTime());
    }



    /**
     * 使用新格式存储患者数据到Redis
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型
     * @param patients 患者列表
     * @return 存储的数据量
     */
    private int storePatientDataWithNewFormat(String hospitalId, String patientType, List<Patient> patients) {
        log.info("使用新格式存储{}个患者[{}]数据到医院[{}]", patients.size(), patientType, hospitalId);

        // 构建Redis键：一个医院+患者类型对应一个键
        String redisKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(patientType) + hospitalId;

        // 先清空该类型的现有数据（全量替换模式）
        redisUtils.delete(redisKey);
        log.info("已清空Redis键: {}", redisKey);

        int validCount = 0;
        int skippedCount = 0;
        Map<String, Object> allPatientData = new HashMap<>();

        for (Patient patient : patients) {
            try {
                // 验证必要字段
                if (patient.getHospitalizationNo() == null || patient.getHospitalizationNo().trim().isEmpty()) {
                    log.debug("患者住院号为空，跳过: ID={}", patient.getInpatientInfoId());
                    skippedCount++;
                    continue;
                }

                if (patient.getInhospitalTime() == null) {
                    log.debug("患者入院时间为空，跳过: ID={}", patient.getInpatientInfoId());
                    skippedCount++;
                    continue;
                }

                // 生成患者唯一标识（作为Hash字段名）
                Long inhospitalTimeStamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
                String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
                    patient.getHospitalizationNo(), inhospitalTimeStamp);

                // 转换患者数据为存储格式
                Map<String, Object> patientData = convertPatientToStorageFormat(patient);

                // 将Map转换为JSON字符串
                String patientJson = JSON.toJSONString(patientData, Feature.WriteNulls, Feature.WriteNullStringAsEmpty);

                // 添加到批量数据中（存储JSON字符串而不是Map对象）
                allPatientData.put(patientKey, patientJson);

                validCount++;
                log.debug("患者数据准备存储: 字段={}", patientKey);

            } catch (Exception e) {
                log.error("处理患者数据时发生异常: ID={}, 错误={}", patient.getInpatientInfoId(), e.getMessage(), e);
                skippedCount++;
            }
        }

        // 批量存储所有患者数据到一个Redis键
        if (!allPatientData.isEmpty()) {
            redisUtils.hSetAll(redisKey, allPatientData);

            // 设置过期时间
            redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);

            log.info("批量存储完成: 键={}, 患者数量={}", redisKey, allPatientData.size());
        }

        log.info("患者[{}]数据新格式存储完成，Redis键: {}, 成功: {}，跳过: {}",
                patientType, redisKey, validCount, skippedCount);
        return validCount;
    }

    /**
     * 使用增量更新模式存储患者数据（用于患者专用接口）
     * 更新已有KEY，插入新KEY，不删除现有数据
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型
     * @param patients 患者列表
     * @return 存储的数据量
     */
    private int storePatientDataWithIncrementalUpdate(String hospitalId, String patientType, List<Patient> patients) {
        log.info("使用增量更新模式存储{}个患者[{}]数据到医院[{}]", patients.size(), patientType, hospitalId);

        // 构建Redis键：一个医院+患者类型对应一个键
        String redisKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(patientType) + hospitalId;

        // 增量更新模式：不删除现有数据，只更新/插入新数据
        log.info("增量更新Redis键: {}", redisKey);

        int validCount = 0;
        int skippedCount = 0;
        Map<String, Object> patientDataToUpdate = new HashMap<>();

        for (Patient patient : patients) {
            try {
                // 验证必要字段
                if (patient.getHospitalizationNo() == null || patient.getHospitalizationNo().trim().isEmpty()) {
                    log.debug("患者住院号为空，跳过: ID={}", patient.getInpatientInfoId());
                    skippedCount++;
                    continue;
                }

                if (patient.getInhospitalTime() == null) {
                    log.debug("患者入院时间为空，跳过: ID={}", patient.getInpatientInfoId());
                    skippedCount++;
                    continue;
                }

                // 生成患者唯一标识（作为Hash字段名）
                Long inhospitalTimeStamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
                String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
                    patient.getHospitalizationNo(), inhospitalTimeStamp);

                // 转换患者数据为存储格式
                Map<String, Object> patientData = convertPatientToStorageFormat(patient);

                // 将Map转换为JSON字符串
                String patientJson = JSON.toJSONString(patientData, Feature.WriteNulls, Feature.WriteNullStringAsEmpty);

                // 添加到批量更新数据中
                patientDataToUpdate.put(patientKey, patientJson);

                validCount++;
                log.debug("患者数据准备增量更新: 字段={}", patientKey);

            } catch (Exception e) {
                log.error("处理患者数据时发生异常: ID={}, 错误={}", patient.getInpatientInfoId(), e.getMessage(), e);
                skippedCount++;
            }
        }

        // 批量增量更新患者数据到Redis
        if (!patientDataToUpdate.isEmpty()) {
            redisUtils.hSetAll(redisKey, patientDataToUpdate);

            // 设置过期时间（如果键不存在的话）
            if (!redisUtils.hasKey(redisKey)) {
                redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);
            }

            log.info("增量更新完成: 键={}, 更新/插入患者数量={}", redisKey, patientDataToUpdate.size());
        }

        log.info("患者[{}]数据增量更新完成，Redis键: {}, 成功: {}，跳过: {}",
                patientType, redisKey, validCount, skippedCount);
        return validCount;
    }

    /**
     * 将患者对象转换为存储格式（按照标准字段顺序）
     */
    private Map<String, Object> convertPatientToStorageFormat(Patient patient) {
        // 使用LinkedHashMap保持字段顺序
        Map<String, Object> data = new LinkedHashMap<>();

        // 按照目标格式的字段顺序
        data.put("name", patient.getName() != null ? patient.getName() : "");
        data.put("id_card", patient.getIdCard() != null ? patient.getIdCard() : "");
        data.put("mobile", patient.getMobile() != null ? patient.getMobile() : "");
        data.put("sex", patient.getSex() != null ? patient.getSex().toString() : "1");
        data.put("age", patient.getAge() != null ? patient.getAge() : 0);
        data.put("hospitalization_no", patient.getHospitalizationNo() != null ? patient.getHospitalizationNo() : "");
        data.put("sickbed_no", patient.getSickbedNo() != null ? patient.getSickbedNo() : "");

        // 时间信息（转换为时间戳）
        if (patient.getInhospitalTime() != null) {
            data.put("inhospital_time", patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC));
        } else {
            data.put("inhospital_time", 0);
        }

        if (patient.getOuthospitalTime() != null) {
            data.put("outhospital_time", patient.getOuthospitalTime().toEpochSecond(java.time.ZoneOffset.UTC));
        } else {
            data.put("outhospital_time", 0);
        }

        data.put("status", patient.getStatus() != null ? patient.getStatus() : 1);
        data.put("category", patient.getCategory() != null ? patient.getCategory() : "");
        data.put("inpatient_ward", patient.getInpatientWard() != null ? patient.getInpatientWard() : "");
        data.put("doctor_id", patient.getDoctorId() != null ? patient.getDoctorId() : "");
        data.put("inpatient_info_id", patient.getInpatientInfoId() != null ? patient.getInpatientInfoId() : "");

        return data;
    }

    /**
     * 处理非患者数据（科室、用户等）
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @param dataList 数据列表
     * @param result 同步结果
     * @param updatedRecords 更新记录列表
     */
    private void processNonPatientData(String hospitalId, String tableName, List<?> dataList, SyncResultDTO result, List<Map<String, Object>> updatedRecords) {
        log.info("开始处理非患者数据，医院ID: {}，表名: {}，数据量: {}", hospitalId, tableName, dataList.size());

        // 将数据存入Redis (使用Hash结构)
        String redisKey = buildRedisKey(hospitalId, tableName);
        log.info("准备将{}个{}数据存入Redis，键: {}", dataList.size(), tableName, redisKey);

        // 创建一个Map来存储数据
        Map<String, Object> dataMap = new HashMap<>();
        int processedCount = 0;
        int validCount = 0;
        int skippedCount = 0;

        for (Object item : dataList) {
            processedCount++;

            // 生成字段名作为唯一标识
            String fieldName = generateFieldName(tableName, item, processedCount - 1);

            if (fieldName == null) {
                log.error("无法为{}数据生成字段名，跳过该数据: {}", tableName, item);
                skippedCount++;
                continue;
            }

            try {
                // 将对象转换为JSON字符串作为值
                String itemJson = JSON.toJSONString(item, Feature.WriteNulls, Feature.WriteNullStringAsEmpty);

                // 添加详细日志，显示JSON内容（仅在调试模式下）
                if (log.isTraceEnabled()) {
                    log.trace("{}JSON数据内容: {}", tableName, itemJson);
                }

                dataMap.put(fieldName, itemJson);
                log.trace("添加{}到Hash: fieldName={}, jsonLength={}", tableName, fieldName, itemJson.length());
                validCount++;
            } catch (Exception e) {
                log.error("将{}数据转换为JSON时发生异常: {}", tableName, e.getMessage(), e);
                skippedCount++;
            }
        }

        if (!dataMap.isEmpty()) {
            log.info("开始将{}数据存入Redis Hash结构: {}", tableName, redisKey);

            try {
                // 全量替换模式：先清理旧数据
                if (redisUtils.hasKey(redisKey)) {
                    log.info("全量替换模式：清理Redis中的旧数据，键: {}", redisKey);
                    redisUtils.delete(redisKey);
                    log.debug("已清理Redis中的旧数据，键: {}", redisKey);
                }

                // 使用hSetAll批量添加数据
                redisUtils.hSetAll(redisKey, dataMap);
                log.debug("{}数据已批量更新到Redis Hash结构，共{}个字段", tableName, dataMap.size());

                // 设置过期时间
                redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);
                log.debug("已设置Redis键的过期时间: {}秒", SyncConstants.ExpireTime.DEFAULT);

                log.info("Redis数据写入成功，所有数据已正确写入");
            } catch (Exception e) {
                log.error("存储{}数据到Redis时发生异常: {}", tableName, e.getMessage(), e);
                throw new BusinessException(ResultCode.ERROR, "存储数据到Redis失败: " + e.getMessage());
            }
        } else {
            log.warn("没有有效的{}数据可存储", tableName);
        }

        // 先计算耗时
        result.setEndTime(LocalDateTime.now());
        long costTime = Duration.between(result.getStartTime(), result.getEndTime()).toMillis();
        result.setCostTime(costTime);

        // 收集性能统计信息
        Map<String, Object> performanceStats = new HashMap<>();
        performanceStats.put("totalProcessingTime", costTime);
        performanceStats.put("averageProcessingTimePerRecord", processedCount > 0 ? costTime / processedCount : 0);
        performanceStats.put("dataValidationTime", "N/A");
        performanceStats.put("redisWriteTime", "N/A");

        // 更新结果（包含详细统计信息）
        result.setStatus(SyncConstants.SyncStatus.SUCCESS);
        result.setMessage(SyncConstants.SyncMessage.SUCCESS);
        result.setCount(validCount);
        result.setTotalCount(processedCount);
        result.setSuccessCount(validCount);
        result.setUpdatedCount(0); // 全量同步不是更新，而是替换
        result.setInsertedCount(validCount); // 全量同步都是新插入
        result.setSkippedCount(skippedCount);
        result.setFailedCount(skippedCount);
        result.setPerformanceStats(performanceStats);
        result.setUpdatedRecords(updatedRecords);

        // 更新最后同步时间
        saveLastSyncTimeToRedis(hospitalId, tableName, result.getEndTime());

        log.info("医院[{}]的[{}]全量同步完成，总处理: {}，成功: {}，插入: {}，跳过: {}，失败: {}，耗时: {}ms",
                hospitalId, tableName, processedCount, validCount, validCount, skippedCount, skippedCount, result.getCostTime());
    }
}