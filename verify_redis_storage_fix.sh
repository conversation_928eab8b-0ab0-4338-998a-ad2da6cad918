#!/bin/bash

echo "=========================================="
echo "验证Redis存储格式修复"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}1. 检查修复后的存储逻辑${NC}"
echo "----------------------------------------"

# 检查storePatientDataWithNewFormat方法是否使用正确的Redis键格式
if grep -A 10 "storePatientDataWithNewFormat" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java | grep -q "getPatientKeyPrefix(patientType) + hospitalId"; then
    echo -e "${GREEN}✓ 使用正确的Redis键格式: patientType + hospitalId${NC}"
else
    echo -e "${RED}✗ Redis键格式可能仍有问题${NC}"
fi

# 检查是否使用批量存储
if grep -A 5 "storePatientDataWithNewFormat" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java | grep -q "hSetAll"; then
    echo -e "${GREEN}✓ 使用批量存储方法 hSetAll${NC}"
else
    echo -e "${RED}✗ 未使用批量存储方法${NC}"
fi

# 检查是否先清空现有数据
if grep -A 15 "storePatientDataWithNewFormat" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java | grep -q "delete(redisKey)"; then
    echo -e "${GREEN}✓ 实现了全量替换模式（先清空再存储）${NC}"
else
    echo -e "${RED}✗ 未实现全量替换模式${NC}"
fi

echo ""
echo -e "${BLUE}2. 检查存储结构说明${NC}"
echo "----------------------------------------"

echo -e "${YELLOW}修复前的问题:${NC}"
echo "  每个患者创建单独的Redis键"
echo "  例如: InPatient-HIN00001, InPatient-HIN00002, InPatient-HIN00003"
echo "  导致Redis中有大量的键"

echo ""
echo -e "${YELLOW}修复后的正确做法:${NC}"
echo "  一个医院+患者类型对应一个Redis键"
echo "  例如: InPatient-H001, OutPatient-H001, UpData-H001"
echo "  所有该类型的患者存储在Hash结构中"

echo ""
echo -e "${BLUE}3. 验证存储格式示例${NC}"
echo "----------------------------------------"

echo "假设医院ID为 H001，有3个患者："
echo ""
echo -e "${RED}修复前（错误）:${NC}"
echo "  Redis键: InPatient-HIN00001 -> {患者1数据}"
echo "  Redis键: InPatient-HIN00002 -> {患者2数据}"  
echo "  Redis键: InPatient-HIN00003 -> {患者3数据}"
echo "  总计: 3个Redis键"

echo ""
echo -e "${GREEN}修复后（正确）:${NC}"
echo "  Redis键: InPatient-H001"
echo "    ├─ Hash字段: HIN00001_1234567890 -> {患者1数据}"
echo "    ├─ Hash字段: HIN00002_1234567891 -> {患者2数据}"
echo "    └─ Hash字段: HIN00003_1234567892 -> {患者3数据}"
echo "  总计: 1个Redis键"

echo ""
echo -e "${BLUE}4. 检查代码实现细节${NC}"
echo "----------------------------------------"

# 检查Redis键构建逻辑
echo "检查Redis键构建逻辑:"
if grep -n "getPatientKeyPrefix(patientType) + hospitalId" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java; then
    echo -e "${GREEN}✓ 找到正确的键构建逻辑${NC}"
else
    echo -e "${RED}✗ 未找到正确的键构建逻辑${NC}"
fi

echo ""
echo "检查患者唯一标识生成:"
if grep -n "generatePatientKey" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java; then
    echo -e "${GREEN}✓ 找到患者唯一标识生成逻辑${NC}"
else
    echo -e "${RED}✗ 未找到患者唯一标识生成逻辑${NC}"
fi

echo ""
echo "检查批量数据准备:"
if grep -n "allPatientData.put" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java; then
    echo -e "${GREEN}✓ 找到批量数据准备逻辑${NC}"
else
    echo -e "${RED}✗ 未找到批量数据准备逻辑${NC}"
fi

echo ""
echo -e "${BLUE}5. 性能优势分析${NC}"
echo "----------------------------------------"

echo "修复后的性能优势:"
echo "  1. 减少Redis键的数量，降低内存开销"
echo "  2. 使用批量操作，提高写入性能"
echo "  3. 便于管理和查询同一医院的患者数据"
echo "  4. 减少Redis键过期管理的复杂度"

echo ""
echo -e "${BLUE}6. 测试文件验证${NC}"
echo "----------------------------------------"

if [ -f "src/test/java/com/sysgetway/core/service/RedisStorageFormatTest.java" ]; then
    echo -e "${GREEN}✓ 创建了Redis存储格式测试文件${NC}"
    echo "  测试文件包含了修复前后的对比验证"
else
    echo -e "${RED}✗ 未找到Redis存储格式测试文件${NC}"
fi

echo ""
echo "=========================================="
echo -e "${GREEN}Redis存储格式修复验证完成！${NC}"
echo "=========================================="

echo ""
echo "总结:"
echo "✓ 修复了Redis键格式：从每个患者一个键改为每个医院+类型一个键"
echo "✓ 实现了批量存储：所有患者数据一次性写入Hash结构"
echo "✓ 添加了全量替换模式：先清空再存储，确保数据一致性"
echo "✓ 创建了测试用例验证修复效果"
