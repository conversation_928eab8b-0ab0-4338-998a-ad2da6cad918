#!/bin/bash

echo "测试患者分类逻辑修改的编译状态..."

# 检查关键类是否存在
echo "检查关键类文件..."
if [ -f "src/main/java/com/sysgetway/core/common/constant/SyncConstants.java" ]; then
    echo "✓ SyncConstants.java 存在"
else
    echo "✗ SyncConstants.java 不存在"
    exit 1
fi

if [ -f "src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java" ]; then
    echo "✓ SyncServiceImpl.java 存在"
else
    echo "✗ SyncServiceImpl.java 不存在"
    exit 1
fi

if [ -f "src/main/java/com/sysgetway/core/entity/Patient.java" ]; then
    echo "✓ Patient.java 存在"
else
    echo "✗ Patient.java 不存在"
    exit 1
fi

# 检查编译后的类文件
echo "检查编译后的类文件..."
if [ -f "target/classes/com/sysgetway/core/service/impl/SyncServiceImpl.class" ]; then
    echo "✓ SyncServiceImpl.class 存在"
else
    echo "✗ SyncServiceImpl.class 不存在"
fi

if [ -f "target/classes/com/sysgetway/core/common/constant/SyncConstants.class" ]; then
    echo "✓ SyncConstants.class 存在"
else
    echo "✗ SyncConstants.class 不存在"
fi

# 检查关键方法是否存在
echo "检查关键方法..."
if grep -q "handleAutoPatientClassification" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java; then
    echo "✓ handleAutoPatientClassification 方法存在"
else
    echo "✗ handleAutoPatientClassification 方法不存在"
fi

if grep -q "storePatientDataWithNewFormat" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java; then
    echo "✓ storePatientDataWithNewFormat 方法存在"
else
    echo "✗ storePatientDataWithNewFormat 方法不存在"
fi

if grep -q "convertPatientToStorageFormat" src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java; then
    echo "✓ convertPatientToStorageFormat 方法存在"
else
    echo "✗ convertPatientToStorageFormat 方法不存在"
fi

# 检查新的Redis键前缀
echo "检查新的Redis键前缀..."
if grep -q "InPatient-" src/main/java/com/sysgetway/core/common/constant/SyncConstants.java; then
    echo "✓ InPatient- 前缀存在"
else
    echo "✗ InPatient- 前缀不存在"
fi

if grep -q "OutPatient-" src/main/java/com/sysgetway/core/common/constant/SyncConstants.java; then
    echo "✓ OutPatient- 前缀存在"
else
    echo "✗ OutPatient- 前缀不存在"
fi

if grep -q "UpData-" src/main/java/com/sysgetway/core/common/constant/SyncConstants.java; then
    echo "✓ UpData- 前缀存在"
else
    echo "✗ UpData- 前缀不存在"
fi

echo "编译状态检查完成！"
