import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 测试Redis存储格式
 */
public class TestRedisStorageFormat {
    
    public static void main(String[] args) {
        System.out.println("测试Redis存储格式...");
        
        // 模拟患者数据
        Map<String, Object> patientData = new LinkedHashMap<>();
        patientData.put("name", "郭杰");
        patientData.put("id_card", "******************");
        patientData.put("mobile", "19166278868");
        patientData.put("sex", "2");
        patientData.put("age", 23);
        patientData.put("hospitalization_no", "HUP08217");
        patientData.put("sickbed_no", "9-072");
        patientData.put("inhospital_time", 1750176294L);
        patientData.put("outhospital_time", 0);
        patientData.put("status", 1);
        patientData.put("category", "自费");
        patientData.put("inpatient_ward", "9病区");
        patientData.put("doctor_id", "D092");
        patientData.put("inpatient_info_id", "PUP08217");
        
        // 模拟FastJSON序列化
        String jsonString = toSimpleJson(patientData);
        
        System.out.println("期望的JSON格式:");
        System.out.println(jsonString);
        
        System.out.println("\n✅ 修复说明:");
        System.out.println("1. 修改了convertPatientToStorageFormat方法，使用LinkedHashMap保持字段顺序");
        System.out.println("2. 修改了storePatientsByTypeNew方法，将Map转换为JSON字符串后存储");
        System.out.println("3. 修改了Redis配置，Hash值使用String序列化器而不是Jackson序列化器");
        System.out.println("4. 这样可以避免Jackson添加类型信息，直接存储纯JSON字符串");
        
        System.out.println("\n🔧 主要修改:");
        System.out.println("- RedisConfig.java: template.setHashValueSerializer(stringRedisSerializer)");
        System.out.println("- SyncServiceImpl.java: 在storePatientsByTypeNew中添加JSON.toJSONString转换");
        System.out.println("- SyncServiceImpl.java: convertPatientToStorageFormat使用LinkedHashMap");
    }
    
    private static String toSimpleJson(Map<String, Object> data) {
        StringBuilder sb = new StringBuilder();
        sb.append("{\n");
        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (!first) {
                sb.append(",\n");
            }
            sb.append("  \"").append(entry.getKey()).append("\": ");
            Object value = entry.getValue();
            if (value instanceof String) {
                sb.append("\"").append(value).append("\"");
            } else {
                sb.append(value);
            }
            first = false;
        }
        sb.append("\n}");
        return sb.toString();
    }
}
