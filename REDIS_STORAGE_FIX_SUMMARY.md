# Redis存储格式修复总结

## 🚨 问题描述

**修复前的问题**：系统为每个患者创建了单独的Redis键，导致Redis中出现大量的键。

例如：
```
InPatient-HIN00001
InPatient-HIN00002  
InPatient-HIN00003
InPatient-HIN00004
...
```

这种做法会导致：
- Redis中键数量爆炸式增长（20000多个键）
- 内存开销巨大
- 管理复杂
- 性能下降

## ✅ 修复方案

**修复后的正确做法**：一个医院ID+患者类型对应一个Redis键，所有该类型的患者存储在Hash结构中。

例如：
```
InPatient-H001 (Hash结构)
├─ HIN00001_1640995200 -> {患者1数据}
├─ HIN00002_1640995300 -> {患者2数据}
├─ HIN00003_1640995400 -> {患者3数据}
└─ ...

OutPatient-H001 (Hash结构)
├─ HIN00005_1640995500 -> {患者5数据}
└─ ...

UpData-H001 (Hash结构)
├─ HIN00001_1640995200 -> {患者1数据}
├─ HIN00003_1640995400 -> {患者3数据}
└─ ...
```

## 🔧 技术实现

### 1. 修改Redis键格式

**修复前**：
```java
String redisKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(patientType) + patient.getHospitalizationNo();
// 结果：InPatient-HIN00001, InPatient-HIN00002, ...
```

**修复后**：
```java
String redisKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(patientType) + hospitalId;
// 结果：InPatient-H001, OutPatient-H001, UpData-H001
```

### 2. 实现批量存储

**修复前**：
```java
for (Patient patient : patients) {
    String redisKey = "InPatient-" + patient.getHospitalizationNo();
    redisUtils.hSet(redisKey, "patientData", patientData);
}
// 每个患者一个Redis键，多次Redis操作
```

**修复后**：
```java
String redisKey = "InPatient-" + hospitalId;
Map<String, Object> allPatientData = new HashMap<>();

for (Patient patient : patients) {
    String patientKey = generatePatientKey(patient.getHospitalizationNo(), timestamp);
    Map<String, Object> patientData = convertPatientToStorageFormat(patient);
    allPatientData.put(patientKey, patientData);
}

redisUtils.hSetAll(redisKey, allPatientData); // 一次批量操作
```

### 3. 患者唯一标识生成

使用住院号+入院时间戳作为Hash字段名：
```java
String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
    patient.getHospitalizationNo(), 
    patient.getInhospitalTime().toEpochSecond(ZoneOffset.UTC)
);
// 结果：HIN00001_1640995200
```

## 📊 修复效果对比

| 项目 | 修复前 | 修复后 | 改善 |
|------|--------|--------|------|
| Redis键数量 | 20000+ | 3个/医院 | 减少99.9% |
| 内存使用 | 高 | 低 | 显著降低 |
| 写入性能 | 慢（多次操作） | 快（批量操作） | 显著提升 |
| 管理复杂度 | 高 | 低 | 大幅简化 |
| 查询效率 | 低 | 高 | 显著提升 |

## 🔍 修复验证

### 1. 代码验证
- ✅ Redis键格式：`patientType + hospitalId`
- ✅ 批量存储：使用`hSetAll`方法
- ✅ 全量替换：先清空再存储
- ✅ 患者唯一标识：住院号+时间戳

### 2. 存储结构验证

**一个医院（H001）有3个患者的存储结构**：

修复前（错误）：
```
Redis键数量：9个
InPatient-HIN00001 -> {患者1数据}
InPatient-HIN00002 -> {患者2数据}  
InPatient-HIN00003 -> {患者3数据}
OutPatient-HIN00001 -> {患者1数据}
OutPatient-HIN00002 -> {患者2数据}
OutPatient-HIN00003 -> {患者3数据}
UpData-HIN00001 -> {患者1数据}
UpData-HIN00002 -> {患者2数据}
UpData-HIN00003 -> {患者3数据}
```

修复后（正确）：
```
Redis键数量：3个
InPatient-H001:
  ├─ HIN00001_1640995200 -> {患者1数据}
  └─ HIN00003_1640995400 -> {患者3数据}

OutPatient-H001:
  └─ HIN00002_1640995300 -> {患者2数据}

UpData-H001:
  ├─ HIN00001_1640995200 -> {患者1数据}
  └─ HIN00003_1640995400 -> {患者3数据}
```

## 🎯 业务逻辑保持不变

修复只改变了存储格式，业务逻辑完全保持不变：

- ✅ Auto患者分类逻辑：status=1 → IN+UP，status=2 → OUT
- ✅ 接口功能：`/sysgetway/api/sync/patient/{hospitalId}/{patientType}`
- ✅ 全量同步：`/sysgetway/api/sync/full/{hospitalId}/{tableName}`
- ✅ 数据完整性：所有患者字段保持不变
- ✅ 查询功能：通过医院ID+患者类型查询

## 📁 相关文件

### 修改的文件
- `src/main/java/com/sysgetway/core/service/impl/SyncServiceImpl.java`
  - 修改`storePatientDataWithNewFormat()`方法

### 测试文件
- `src/test/java/com/sysgetway/core/service/RedisStorageFormatTest.java`
- `verify_redis_storage_fix.sh`

## 🚀 部署建议

1. **数据备份**：部署前备份现有Redis数据
2. **清理旧数据**：可以考虑清理旧格式的Redis键
3. **监控**：部署后监控Redis键数量和内存使用
4. **验证**：确认新格式下的数据查询功能正常

## ✅ 总结

此次修复成功解决了Redis键数量爆炸的问题，将存储格式从"每个患者一个键"改为"每个医院+类型一个键"，大幅降低了Redis的内存使用和管理复杂度，同时提升了系统性能。修复后的系统更加高效、可维护，并且完全保持了原有的业务功能。
