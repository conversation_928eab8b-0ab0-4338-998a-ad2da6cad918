package com.sysgetway.core.service;

import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.entity.Patient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 患者分类逻辑测试
 */
public class PatientClassificationTest {

    private List<Patient> testPatients;

    @BeforeEach
    public void setUp() {
        testPatients = new ArrayList<>();
        
        // 创建测试患者数据
        // 在院患者 (status=1)
        Patient inPatient = new Patient();
        inPatient.setInpatientInfoId("1001");
        inPatient.setName("张三");
        inPatient.setHospitalizationNo("1000000000385");
        inPatient.setInhospitalTime(LocalDateTime.now().minusDays(5));
        inPatient.setStatus(1); // 在院
        testPatients.add(inPatient);
        
        // 出院患者 (status=2)
        Patient outPatient = new Patient();
        outPatient.setInpatientInfoId("1002");
        outPatient.setName("李四");
        outPatient.setHospitalizationNo("1000000000390");
        outPatient.setInhospitalTime(LocalDateTime.now().minusDays(10));
        outPatient.setOuthospitalTime(LocalDateTime.now().minusDays(2));
        outPatient.setStatus(2); // 出院
        testPatients.add(outPatient);
        
        // 另一个在院患者
        Patient inPatient2 = new Patient();
        inPatient2.setInpatientInfoId("1003");
        inPatient2.setName("王五");
        inPatient2.setHospitalizationNo("1000000000232");
        inPatient2.setInhospitalTime(LocalDateTime.now().minusDays(3));
        inPatient2.setStatus(1); // 在院
        testPatients.add(inPatient2);
    }

    @Test
    public void testPatientTypeValidation() {
        // 测试患者类型验证
        assertTrue(SyncConstants.PatientType.isValid(SyncConstants.PatientType.IN));
        assertTrue(SyncConstants.PatientType.isValid(SyncConstants.PatientType.OUT));
        assertTrue(SyncConstants.PatientType.isValid(SyncConstants.PatientType.UP));
        assertTrue(SyncConstants.PatientType.isValid(SyncConstants.PatientType.AUTO));
        assertFalse(SyncConstants.PatientType.isValid("invalid"));
    }

    @Test
    public void testStorageTypeCheck() {
        // 测试存储类型检查
        assertTrue(SyncConstants.PatientType.isStorageType(SyncConstants.PatientType.IN));
        assertTrue(SyncConstants.PatientType.isStorageType(SyncConstants.PatientType.OUT));
        assertTrue(SyncConstants.PatientType.isStorageType(SyncConstants.PatientType.UP));
        assertFalse(SyncConstants.PatientType.isStorageType(SyncConstants.PatientType.AUTO));
    }

    @Test
    public void testRedisKeyGeneration() {
        // 测试Redis键生成
        String inKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.IN);
        String outKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.OUT);
        String upKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.UP);
        
        assertEquals("InPatient-", inKey);
        assertEquals("OutPatient-", outKey);
        assertEquals("UpData-", upKey);
    }

    @Test
    public void testPatientKeyGeneration() {
        Patient patient = testPatients.get(0);
        
        // 测试患者唯一标识生成
        Long timestamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
        String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
            patient.getHospitalizationNo(), timestamp);
        
        assertNotNull(patientKey);
        assertTrue(patientKey.contains(patient.getHospitalizationNo()));
        assertTrue(patientKey.contains(timestamp.toString()));
    }

    @Test
    public void testRedisKeyGeneration2() {
        Patient patient = testPatients.get(0);
        
        // 测试完整Redis键生成
        String redisKey = SyncConstants.PatientKeyGenerator.generateRedisKey(
            SyncConstants.PatientType.IN, patient.getHospitalizationNo());
        
        assertEquals("InPatient-" + patient.getHospitalizationNo(), redisKey);
    }

    @Test
    public void testPatientClassificationLogic() {
        // 测试患者分类逻辑
        int inPatientCount = 0;
        int outPatientCount = 0;
        
        for (Patient patient : testPatients) {
            if (patient.getStatus() != null) {
                switch (patient.getStatus()) {
                    case 1:
                        // 在院患者应该同时存储到IN和UP
                        inPatientCount++;
                        break;
                    case 2:
                        // 出院患者应该存储到OUT
                        outPatientCount++;
                        break;
                }
            }
        }
        
        assertEquals(2, inPatientCount); // 两个在院患者
        assertEquals(1, outPatientCount); // 一个出院患者
    }

    @Test
    public void testPatientDataValidation() {
        // 测试患者数据验证
        for (Patient patient : testPatients) {
            assertNotNull(patient.getHospitalizationNo(), "住院号不能为空");
            assertNotNull(patient.getInhospitalTime(), "入院时间不能为空");
            assertNotNull(patient.getStatus(), "患者状态不能为空");
        }
    }
}
