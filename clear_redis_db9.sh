#!/bin/bash

# Redis DB9 数据清理脚本
# 用于删除Redis数据库9中的所有数据

# 配置参数
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_DB="9"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    printf "${GREEN}[INFO]${NC} %s\n" "$1"
}

print_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

print_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 检查redis-cli是否可用
check_redis_cli() {
    if ! command -v redis-cli &> /dev/null; then
        print_error "redis-cli 命令未找到"
        print_info "在Mac上安装redis-cli的方法："
        print_info "1. 使用Homebrew: brew install redis"
        print_info "2. 或者使用MacPorts: sudo port install redis"
        print_info "3. 安装完成后重新运行此脚本"
        exit 1
    fi
}

# 构建redis-cli连接参数
build_redis_cmd() {
    local cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT -n $REDIS_DB"
    if [ -n "$REDIS_PASSWORD" ]; then
        cmd="$cmd -a $REDIS_PASSWORD"
    fi
    echo "$cmd"
}

# 测试Redis连接
test_connection() {
    local redis_cmd=$(build_redis_cmd)
    print_info "测试Redis连接..."
    
    if ! $redis_cmd ping > /dev/null 2>&1; then
        print_error "无法连接到Redis服务器 $REDIS_HOST:$REDIS_PORT"
        print_error "请检查Redis服务是否运行，以及连接参数是否正确"
        exit 1
    fi
    
    print_info "Redis连接成功"
}

# 获取DB9中的键数量
get_key_count() {
    local redis_cmd=$(build_redis_cmd)
    local count=$($redis_cmd DBSIZE 2>/dev/null)
    echo "$count"
}

# 清理DB9数据
clear_db9() {
    local redis_cmd=$(build_redis_cmd)
    
    print_info "开始清理Redis DB$REDIS_DB 的数据..."
    
    # 获取清理前的键数量
    local before_count=$(get_key_count)
    print_info "清理前DB$REDIS_DB中有 $before_count 个键"
    
    if [ "$before_count" -eq 0 ]; then
        print_info "DB$REDIS_DB 已经是空的，无需清理"
        return 0
    fi
    
    # 执行FLUSHDB命令清理当前数据库
    if $redis_cmd FLUSHDB > /dev/null 2>&1; then
        local after_count=$(get_key_count)
        print_info "清理完成！DB$REDIS_DB 现在有 $after_count 个键"
        print_info "成功删除了 $((before_count - after_count)) 个键"
    else
        print_error "清理失败，请检查Redis权限设置"
        exit 1
    fi
}

# 主函数
main() {
    echo "======================================="
    echo "Redis DB9 数据清理脚本"
    echo "======================================="
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                REDIS_HOST="$2"
                shift 2
                ;;
            -p|--port)
                REDIS_PORT="$2"
                shift 2
                ;;
            -a|--password)
                REDIS_PASSWORD="$2"
                shift 2
                ;;
            -d|--database)
                REDIS_DB="$2"
                shift 2
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  -h, --host      Redis主机地址 (默认: localhost)"
                echo "  -p, --port      Redis端口 (默认: 6379)"
                echo "  -a, --password  Redis密码 (默认: 无密码)"
                echo "  -d, --database  数据库编号 (默认: 9)"
                echo "  --help          显示此帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
    
    print_info "连接参数: $REDIS_HOST:$REDIS_PORT, DB$REDIS_DB"
    
    # 确认操作
    print_warning "此操作将删除Redis DB$REDIS_DB 中的所有数据！"
    read -p "确认继续吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi
    
    # 执行清理
    check_redis_cli
    test_connection
    clear_db9
    
    print_info "脚本执行完成"
}

# 执行主函数
main "$@"
