# [cite_start]数据视图结构说明 [cite: 1]

## [cite_start]1. view_spt_department(医护人员账号视图) [cite: 2]

| 字段名       | 类型        | 说明                                   |
| :----------- | :---------- | :------------------------------------- |
| department_id | Int(11)     | 科室 ID                                |
| code         | varchar(50) | 科室编码                               |
| name         | Varchar(50) | 科室名称                               |
| updated_at   | Datetime    | 数据更新时间,若无更新和创建时间一致    |

## [cite_start]2. view_spt_user(医护人员账号管理视图) [cite: 4]

| 字段名         | 类型         | 说明                                                         |
| :------------- | :----------- | :----------------------------------------------------------- |
| user_name      | Varchar(50)  | 医护人员账号(可以是工号)                                     |
| name           | Varchar(50)  | 姓名                                                         |
| sex            | int(2)       | 性别 1.男2.女                                                |
| role_id        | $Int(2)$     | 1.医生2.护士                                                 |
| dept_id        | $Int(11)$    | 科室 ID,与科室视图中 department_id 对应                      |
| updated_at     | Datetime     | 数据更新时间,若无更新和创建时间一致                          |
| inpatient_ward | Varchar(500) | 所管理的病区,如果有多个使用半角逗号分隔,如:1 病区,2病区    |
| Mobile         | Varchar(20)  | 医生手机号                                                   |

## [cite_start]3. view_spt_patient(患者信息视图) [cite: 6]

| 字段名              | 类型         | 说明                                                   |
| :------------------ | :----------- | :----------------------------------------------------- |
| name                | Varchar(32)  | 患者姓名                                               |
| id card             | Varchar(32)  | 身份证号                                               |
| mobile              | varchar(32)  | 手机号码                                               |
| sex                 | $int(2)$     | 性别1.男2.女                                           |
| age                 | $int(2)$     | 数字格式的年龄                                         |
| birthday            | Datetime     | 生日(格式:YYYY-MM-dd)                                 |
| hospitalization_no  | Varchar(32)  | 住院号,患者出入院多次,始终绑定该患者,不变              |
| inhospital_diagnose | Varchar(255) | 入院诊断                                               |
| dept_id             | Int(11)      | 科室 ID,与科室视图中 department_id 对应                |
| sickbed_no          | Varchar(32)  | 床位号                                                 |
| doctor_id           | Varchar(32)  | 责任医生的用户ID,对应用户视图表中的user_name(可以是工号) |
| nurse_id            | Varchar(32)  | 责任护士的用户ID,对应用户视图表中的user_name(可以是工号) |
| nurse_level         | Int(2)       | 护理级别1.一级2.二级3.三级4.特级护理                   |
| inhospital_time     | Datetime     | 入院时间                                               |
| outhospital_time    | Datetime     | 出院时间                                               |
| status              | Int(2)       | 患者当前状态1.在院2.出院                               |
| catetegory          | Varchar(32)  | 患者类别如,医保 自费其他等                             |
| inpatient_ward      | Varchar(32)  | 病区 如男病区 女病区一病区等                           |
| inpatient_info_id   | Varchar(32)  | 信息唯一ID,每次出入院一个ID编号                        |
| updated_at          | Datetime     | 数据更新时间,若无更新和创建时间一致                    |