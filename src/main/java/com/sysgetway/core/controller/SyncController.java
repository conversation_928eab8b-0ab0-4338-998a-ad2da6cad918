package com.sysgetway.core.controller;

import com.sysgetway.core.common.constant.ResultCode;
import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.model.dto.SyncResultDTO;
import com.sysgetway.core.service.SyncService;
import com.sysgetway.core.service.impl.SyncServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据同步控制器
 */
@RestController
@RequestMapping("/api/sync")
@Api(tags = "数据同步接口")
@Slf4j
public class SyncController {

    @Resource
    private SyncService syncService;
    
    /**
     * 触发全量同步
     *
     * @param hospitalId 医院ID
     * @param tableName 表名(department, user, patient)
     * @param dataList 数据列表（可选，如果不提供则从外部系统获取）
     * @return 响应结果
     */
    @PutMapping("/full/{hospitalId}/{tableName}")
    @ApiOperation(value = "触发全量同步", notes = "触发指定医院指定表的全量同步数据，清除原有数据并存储到Redis中。tableName可选值：department(科室), user(医护人员), patient(患者)。支持异步处理，立即返回任务状态。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "tableName", value = "表名", required = true, paramType = "path",
                    allowableValues = "department,user,patient")
    })
    public ResponseResult<SyncResultDTO> syncFull(
            @PathVariable String hospitalId,
            @PathVariable String tableName,
            @ApiParam(value = "数据列表（可选，JSON格式）", required = false)
            @RequestBody(required = false) List<Object> dataList) {
        long startTime = System.currentTimeMillis();
        int dataSize = (dataList != null ? dataList.size() : 0);
        log.info("接收到全量同步请求，医院ID: {}，表名: {}，数据量: {}", hospitalId, tableName, dataSize);

        // 记录请求大小
        if (dataSize > 0) {
            log.info("全量同步数据请求大小估计: 约{}KB", dataSize * 0.5); // 估算每个对象约0.5KB
        }

        // 快速验证参数
        if (!SyncConstants.TableName.isValid(tableName)) {
            log.error("无效的表名: {}", tableName);
            return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "无效的表名: " + tableName);
        }

        // 调用service方法，使用异步处理
        ResponseResult<SyncResultDTO> result = syncService.syncFullAsync(hospitalId, tableName, dataList);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 记录接口处理时间
        log.info("全量同步请求初始化完成，医院ID: {}，表名: {}，数据量: {}，接口耗时: {}ms",
                hospitalId, tableName, dataSize, duration);

        return result;
    }
    
    /**
     * 触发增量同步
     *
     * @param hospitalId 医院ID
     * @param tableName 表名(department, user, patient)
     * @return 响应结果
     */
    @PutMapping("/incremental/{hospitalId}/{tableName}")
    @ApiOperation(value = "触发增量同步", notes = "触发指定医院指定表的增量同步数据，并存储到Redis中。tableName可选值：department(科室), user(医护人员), patient(患者)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "tableName", value = "表名", required = true, paramType = "path", 
                    allowableValues = "department,user,patient")
    })
    public ResponseResult<SyncResultDTO> syncIncremental(@PathVariable String hospitalId, @PathVariable String tableName) {
        long startTime = System.currentTimeMillis();
        log.info("接收到增量同步请求，医院ID: {}，表名: {}", hospitalId, tableName);
        
        ResponseResult<SyncResultDTO> result = syncService.syncIncremental(hospitalId, tableName);
        
        long endTime = System.currentTimeMillis();
        log.info("增量同步请求处理完成，医院ID: {}，表名: {}，耗时: {}ms", hospitalId, tableName, (endTime - startTime));
        
        return result;
    }
    
    /**
     * 触发患者分类同步
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out, auto)
     * @param patients 患者数据列表（符合数据视图结构说明）
     * @return 响应结果
     */
    @PutMapping("/patient/{hospitalId}/{patientType}")
    @ApiOperation(value = "触发患者分类同步", notes = "触发指定医院特定类型患者的同步数据，并存储到Redis中。patientType可选值：in(入院患者), up(在院患者), out(出院患者), auto(自动分类)。请求体中可以包含符合数据视图结构说明的患者数据列表。当patientType为auto时，系统会自动根据患者状态分类：status=1(在院)同时存储到in和up类型，status=2(出院)存储到out类型。新的存储格式使用Redis键：InPatient-住院号、OutPatient-住院号、UpData-住院号，患者唯一标识为住院号+入院时间戳，存储对象而非JSON字符串。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "patientType", value = "患者类型", required = true, paramType = "path", 
                    allowableValues = "in,up,out,auto")
    })
    public ResponseResult<SyncResultDTO> syncPatientByType(
            @PathVariable String hospitalId, 
            @PathVariable String patientType,
            @ApiParam(value = "患者数据列表，符合数据视图结构说明(view_spt_patient)。包含字段：inpatientInfoId(信息唯一ID), name(患者姓名), idCard(身份证号), mobile(手机号码), sex(性别), age(年龄), birthday(生日), hospitalizationNo(住院号), inhospitalDiagnose(入院诊断), deptId(科室ID), sickbedNo(床位号), doctorId(责任医生ID), nurseId(责任护士ID), nurseLevel(护理级别), inhospitalTime(入院时间), outhospitalTime(出院时间), status(患者当前状态), category(患者类别), inpatientWard(病区), updatedAt(更新时间)", 
                    required = false, 
                    example = "[{\"inpatientInfoId\":\"P001\",\"name\":\"张三\",\"idCard\":\"110101199001011234\",\"mobile\":\"13800138006\",\"sex\":1,\"age\":30,\"hospitalizationNo\":\"H001\",\"inhospitalDiagnose\":\"高血压\",\"deptId\":2,\"sickbedNo\":\"2-101\",\"doctorId\":\"D002\",\"nurseId\":\"N002\",\"nurseLevel\":2,\"inhospitalTime\":\"2023-06-10T10:00:00\",\"outhospitalTime\":null,\"status\":1,\"category\":\"医保\",\"inpatientWard\":\"2病区\",\"updatedAt\":\"2023-06-10T10:00:00\"}]") 
            @RequestBody(required = false) List<Patient> patients) {
        long startTime = System.currentTimeMillis();
        int dataSize = (patients != null ? patients.size() : 0);
        log.info("接收到患者分类同步请求，医院ID: {}，患者类型: {}, 患者数据数量: {}", 
                hospitalId, patientType, dataSize);
        
        // 记录请求大小
        if (dataSize > 0) {
            log.info("患者数据请求大小估计: 约{}KB", dataSize * 0.5); // 估算每个患者对象约0.5KB
        }
        
        // 快速验证参数
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "无效的患者类型: " + patientType);
        }
        
        // 立即调用service方法获取初始状态和任务ID，使用异步方法
        ResponseResult<SyncResultDTO> result = syncService.syncPatientByTypeAsync(hospitalId, patientType, patients);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 记录接口处理时间
        log.info("患者分类同步请求初始化完成，医院ID: {}，患者类型: {}, 数据量: {}, 接口耗时: {}ms", 
                hospitalId, patientType, dataSize, duration);
        
        return result;
    }
    
    /**
     * 获取同步状态
     *
     * @param hospitalId 医院ID
     * @param tableName 表名或患者类型前缀(department, user, patient, patient:in, patient:up, patient:out)
     * @return 响应结果
     */
    @GetMapping("/status/{hospitalId}/{tableName}")
    @ApiOperation(value = "获取同步状态", notes = "获取指定医院指定表的同步状态。tableName可选值：department(科室), user(医护人员), patient(患者), patient:in(入院患者), patient:up(在院患者), patient:out(出院患者)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "tableName", value = "表名或患者类型前缀", required = true, paramType = "path", 
                    allowableValues = "department,user,patient,patient:in,patient:up,patient:out")
    })
    public ResponseResult<SyncResultDTO> getSyncStatus(@PathVariable String hospitalId, @PathVariable String tableName) {
        log.info("接收到获取同步状态请求，医院ID: {}，表名: {}", hospitalId, tableName);
        return syncService.getSyncStatus(hospitalId, tableName);
    }
    
    /**
     * 获取最后同步时间
     *
     * @param hospitalId 医院ID
     * @param tableName 表名或患者类型前缀(department, user, patient, patient:in, patient:up, patient:out)
     * @return 响应结果
     */
    @GetMapping("/last-time/{hospitalId}/{tableName}")
    @ApiOperation(value = "获取最后同步时间", notes = "获取指定医院指定表的最后同步时间。tableName可选值：department(科室), user(医护人员), patient(患者), patient:in(入院患者), patient:up(在院患者), patient:out(出院患者)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "tableName", value = "表名或患者类型前缀", required = true, paramType = "path", 
                    allowableValues = "department,user,patient,patient:in,patient:up,patient:out")
    })
    public ResponseResult<String> getLastSyncTime(@PathVariable String hospitalId, @PathVariable String tableName) {
        log.info("接收到获取最后同步时间请求，医院ID: {}，表名: {}", hospitalId, tableName);
        return syncService.getLastSyncTime(hospitalId, tableName);
    }
    
    /**
     * 清理任务状态
     *
     * @param hospitalId 医院ID
     * @param tableNameOrType 表名或患者类型
     * @param syncType 同步类型
     * @return 响应结果
     */
    @DeleteMapping("/task/{hospitalId}/{tableNameOrType}/{syncType}")
    @ApiOperation(value = "清理任务状态", notes = "清理指定医院指定表的任务状态。tableNameOrType可选值：department(科室), user(医护人员), patient(患者), in(入院患者), up(在院患者), out(出院患者)。syncType可选值：full(全量同步), incremental(增量同步), patient(患者同步)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "tableNameOrType", value = "表名或患者类型", required = true, paramType = "path", 
                    allowableValues = "department,user,patient,in,up,out"),
            @ApiImplicitParam(name = "syncType", value = "同步类型", required = true, paramType = "path", 
                    allowableValues = "full,incremental,patient")
    })
    public ResponseResult<String> clearTaskStatus(
            @PathVariable String hospitalId, 
            @PathVariable String tableNameOrType,
            @PathVariable String syncType) {
        log.info("接收到清理任务状态请求，医院ID: {}，表名或患者类型: {}, 同步类型: {}", 
                hospitalId, tableNameOrType, syncType);
        return syncService.clearTaskStatus(hospitalId, tableNameOrType, syncType);
    }
    
    /**
     * 清理所有任务状态
     *
     * @return 响应结果
     */
    @DeleteMapping("/task/all")
    @ApiOperation(value = "清理所有任务状态", notes = "清理所有任务状态")
    public ResponseResult<String> clearAllTaskStatus() {
        log.info("接收到清理所有任务状态请求");
        return syncService.clearAllTaskStatus();
    }

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 响应结果
     */
    @GetMapping("/task/{taskId}")
    @ApiOperation(value = "获取任务状态", notes = "通过任务ID获取同步任务的状态和更新的数据列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务ID", required = true, paramType = "path")
    })
    public ResponseResult<SyncResultDTO> getTaskStatus(@PathVariable String taskId) {
        log.info("接收到获取任务状态请求，任务ID: {}", taskId);
        return syncService.getTaskStatus(taskId);
    }
    
    /**
     * 触发患者自动分类同步（不需要指定患者类型）
     *
     * @param hospitalId 医院ID
     * @param patients 患者数据列表（符合数据视图结构说明）
     * @return 响应结果
     */
    @PutMapping("/patient/{hospitalId}")
    @ApiOperation(value = "触发患者自动分类同步", notes = "触发指定医院患者的同步数据，并根据患者状态自动分类存储到Redis中。系统会自动根据患者状态分类：status=1归为up，status=2归为out，且所有in类型的患者（有入院时间无出院时间）也会归入up类别。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path")
    })
    public ResponseResult<SyncResultDTO> syncPatientAutoClassify(
            @PathVariable String hospitalId,
            @ApiParam(value = "患者数据列表，符合数据视图结构说明(view_spt_patient)。包含字段：inpatientInfoId(信息唯一ID), name(患者姓名), idCard(身份证号), mobile(手机号码), sex(性别), age(年龄), birthday(生日), hospitalizationNo(住院号), inhospitalDiagnose(入院诊断), deptId(科室ID), sickbedNo(床位号), doctorId(责任医生ID), nurseId(责任护士ID), nurseLevel(护理级别), inhospitalTime(入院时间), outhospitalTime(出院时间), status(患者当前状态), category(患者类别), inpatientWard(病区), updatedAt(更新时间)", 
                    required = false, 
                    example = "[{\"inpatientInfoId\":\"P001\",\"name\":\"张三\",\"idCard\":\"110101199001011234\",\"mobile\":\"13800138006\",\"sex\":1,\"age\":30,\"hospitalizationNo\":\"H001\",\"inhospitalDiagnose\":\"高血压\",\"deptId\":2,\"sickbedNo\":\"2-101\",\"doctorId\":\"D002\",\"nurseId\":\"N002\",\"nurseLevel\":2,\"inhospitalTime\":\"2023-06-10T10:00:00\",\"outhospitalTime\":null,\"status\":1,\"category\":\"医保\",\"inpatientWard\":\"2病区\",\"updatedAt\":\"2023-06-10T10:00:00\"}]") 
            @RequestBody(required = false) List<Patient> patients) {
        long startTime = System.currentTimeMillis();
        int dataSize = (patients != null ? patients.size() : 0);
        log.info("接收到患者自动分类同步请求，医院ID: {}, 患者数据数量: {}", hospitalId, dataSize);
        
        // 记录请求大小
        if (dataSize > 0) {
            log.info("患者数据请求大小估计: 约{}KB", dataSize * 0.5); // 估算每个患者对象约0.5KB
        }
        
        // 使用 AUTO 类型调用现有的同步方法
        ResponseResult<SyncResultDTO> result = syncService.syncPatientByType(hospitalId, SyncConstants.PatientType.AUTO, patients);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 记录详细的性能指标
        log.info("患者自动分类同步请求处理完成，医院ID: {}, 数据量: {}, 总耗时: {}ms, 平均每条数据耗时: {}ms", 
                hospitalId, dataSize, duration, (dataSize > 0 ? (float)duration / dataSize : 0));
        
        // 如果耗时超过阈值，记录警告日志
        if (duration > 10000) { // 超过10秒
            log.warn("患者自动分类同步请求处理时间过长，医院ID: {}, 数据量: {}, 耗时: {}ms", 
                    hospitalId, dataSize, duration);
        }
        
        return result;
    }
    
    /**
     * 测试将患者数据存入Redis Hash结构
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @param patient 患者数据
     * @return 响应结果
     */
    @PutMapping("/test/redis/{hospitalId}/{patientType}")
    @ApiOperation(value = "测试Redis患者数据存储", notes = "测试将患者数据直接存入Redis Hash结构。patientType可选值：in(入院患者), up(在院患者), out(出院患者)。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "patientType", value = "患者类型", required = true, paramType = "path", 
                    allowableValues = "in,up,out")
    })
    public ResponseResult<String> testRedisPatientStore(
            @PathVariable String hospitalId, 
            @PathVariable String patientType,
            @ApiParam(value = "患者数据，符合数据视图结构说明(view_spt_patient)。", required = true) 
            @RequestBody Patient patient) {
        
        log.info("接收到Redis患者数据存储测试请求，医院ID: {}, 患者类型: {}, 患者ID: {}", 
                hospitalId, patientType, patient.getInpatientInfoId());
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            return ResponseResult.error("无效的患者类型: " + patientType);
        }
        
        // 使用SyncService中的测试方法
        boolean success = ((SyncServiceImpl) syncService).testSavePatientToRedisHash(hospitalId, patientType, patient);
        
        if (success) {
            return ResponseResult.success("患者数据成功存入Redis Hash结构");
        } else {
            return ResponseResult.error("患者数据存入Redis Hash结构失败，请查看日志");
        }
    }
} 