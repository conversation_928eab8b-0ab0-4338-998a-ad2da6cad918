package com.sysgetway.core.controller;

import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.entity.Patient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步控制器集成测试
 */
public class SyncControllerIntegrationTest {

    private List<Patient> testPatients;

    @BeforeEach
    public void setUp() {
        testPatients = new ArrayList<>();
        
        // 创建测试患者数据
        // 在院患者 (status=1)
        Patient inPatient = new Patient();
        inPatient.setInpatientInfoId("1001");
        inPatient.setName("张三");
        inPatient.setHospitalizationNo("1000000000385");
        inPatient.setInhospitalTime(LocalDateTime.now().minusDays(5));
        inPatient.setStatus(1); // 在院
        inPatient.setIdCard("110101199001011234");
        inPatient.setMobile("13800138001");
        inPatient.setSex(1);
        inPatient.setAge(30);
        testPatients.add(inPatient);
        
        // 出院患者 (status=2)
        Patient outPatient = new Patient();
        outPatient.setInpatientInfoId("1002");
        outPatient.setName("李四");
        outPatient.setHospitalizationNo("1000000000390");
        outPatient.setInhospitalTime(LocalDateTime.now().minusDays(10));
        outPatient.setOuthospitalTime(LocalDateTime.now().minusDays(2));
        outPatient.setStatus(2); // 出院
        outPatient.setIdCard("110101199002021234");
        outPatient.setMobile("13800138002");
        outPatient.setSex(2);
        outPatient.setAge(25);
        testPatients.add(outPatient);
        
        // 另一个在院患者
        Patient inPatient2 = new Patient();
        inPatient2.setInpatientInfoId("1003");
        inPatient2.setName("王五");
        inPatient2.setHospitalizationNo("1000000000232");
        inPatient2.setInhospitalTime(LocalDateTime.now().minusDays(3));
        inPatient2.setStatus(1); // 在院
        inPatient2.setIdCard("110101199003031234");
        inPatient2.setMobile("13800138003");
        inPatient2.setSex(1);
        inPatient2.setAge(35);
        testPatients.add(inPatient2);
    }

    @Test
    public void testPatientDataStructure() {
        // 测试患者数据结构是否符合接口要求
        for (Patient patient : testPatients) {
            // 必需字段检查
            assertNotNull(patient.getInpatientInfoId(), "inpatientInfoId不能为空");
            assertNotNull(patient.getName(), "name不能为空");
            assertNotNull(patient.getHospitalizationNo(), "hospitalizationNo不能为空");
            assertNotNull(patient.getInhospitalTime(), "inhospitalTime不能为空");
            assertNotNull(patient.getStatus(), "status不能为空");
            
            // 可选字段检查
            assertTrue(patient.getIdCard() == null || patient.getIdCard().length() > 0, "idCard如果不为空则应有内容");
            assertTrue(patient.getMobile() == null || patient.getMobile().length() > 0, "mobile如果不为空则应有内容");
            assertTrue(patient.getSex() == null || (patient.getSex() >= 1 && patient.getSex() <= 2), "sex应为1或2");
            assertTrue(patient.getAge() == null || patient.getAge() >= 0, "age应为非负数");
        }
    }

    @Test
    public void testPatientClassificationLogic() {
        // 测试患者分类逻辑是否正确
        int expectedInPatients = 0;
        int expectedUpPatients = 0;
        int expectedOutPatients = 0;
        
        for (Patient patient : testPatients) {
            if (patient.getStatus() != null) {
                switch (patient.getStatus()) {
                    case 1:
                        // 在院患者应该同时存储到IN和UP
                        expectedInPatients++;
                        expectedUpPatients++;
                        break;
                    case 2:
                        // 出院患者应该存储到OUT
                        expectedOutPatients++;
                        break;
                }
            }
        }
        
        assertEquals(2, expectedInPatients, "应该有2个IN类型患者");
        assertEquals(2, expectedUpPatients, "应该有2个UP类型患者");
        assertEquals(1, expectedOutPatients, "应该有1个OUT类型患者");
    }

    @Test
    public void testRedisKeyFormat() {
        // 测试新的Redis键格式
        for (Patient patient : testPatients) {
            String hospitalizationNo = patient.getHospitalizationNo();
            
            // 测试不同类型的Redis键格式
            String inKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.IN) + hospitalizationNo;
            String outKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.OUT) + hospitalizationNo;
            String upKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.UP) + hospitalizationNo;
            
            assertEquals("InPatient-" + hospitalizationNo, inKey);
            assertEquals("OutPatient-" + hospitalizationNo, outKey);
            assertEquals("UpData-" + hospitalizationNo, upKey);
        }
    }

    @Test
    public void testPatientUniqueIdentifier() {
        // 测试患者唯一标识生成
        for (Patient patient : testPatients) {
            if (patient.getHospitalizationNo() != null && patient.getInhospitalTime() != null) {
                Long timestamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
                String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
                    patient.getHospitalizationNo(), timestamp);
                
                assertNotNull(patientKey, "患者唯一标识不能为空");
                assertTrue(patientKey.contains(patient.getHospitalizationNo()), "患者唯一标识应包含住院号");
                assertTrue(patientKey.contains(timestamp.toString()), "患者唯一标识应包含时间戳");
            }
        }
    }

    @Test
    public void testApiParameterValidation() {
        // 测试API参数验证
        
        // 测试有效的患者类型
        assertTrue(SyncConstants.PatientType.isValid("in"));
        assertTrue(SyncConstants.PatientType.isValid("out"));
        assertTrue(SyncConstants.PatientType.isValid("up"));
        assertTrue(SyncConstants.PatientType.isValid("auto"));
        
        // 测试无效的患者类型
        assertFalse(SyncConstants.PatientType.isValid("invalid"));
        assertFalse(SyncConstants.PatientType.isValid(""));
        assertFalse(SyncConstants.PatientType.isValid(null));
        
        // 测试存储类型检查
        assertTrue(SyncConstants.PatientType.isStorageType("in"));
        assertTrue(SyncConstants.PatientType.isStorageType("out"));
        assertTrue(SyncConstants.PatientType.isStorageType("up"));
        assertFalse(SyncConstants.PatientType.isStorageType("auto"));
    }

    @Test
    public void testDataSizeEstimation() {
        // 测试数据大小估算（用于接口日志记录）
        int dataSize = testPatients.size();
        double estimatedSizeKB = dataSize * 0.5; // 每个患者对象约0.5KB
        
        assertTrue(estimatedSizeKB > 0, "估算大小应大于0");
        assertEquals(1.5, estimatedSizeKB, 0.1, "3个患者对象估算约1.5KB");
    }

    @Test
    public void testPatientDataIntegrity() {
        // 测试患者数据完整性
        for (Patient patient : testPatients) {
            // 检查关键业务字段的一致性
            if (patient.getStatus() != null && patient.getStatus() == 2) {
                // 出院患者应该有出院时间
                // 注意：这个检查可能需要根据实际业务规则调整
                if (patient.getOuthospitalTime() != null) {
                    assertTrue(patient.getOuthospitalTime().isAfter(patient.getInhospitalTime()),
                            "出院时间应晚于入院时间");
                }
            }
            
            // 检查年龄的合理性
            if (patient.getAge() != null) {
                assertTrue(patient.getAge() >= 0 && patient.getAge() <= 150, "年龄应在合理范围内");
            }
        }
    }
}
