import com.alibaba.fastjson2.JSON;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 简单测试Patient修复
 */
public class TestPatientFix {
    
    public static void main(String[] args) {
        System.out.println("测试Patient数据修复...");
        
        // 测试时间转换
        String isoTime = "2025-07-05T09:29:52.833085";
        Long unixTimestamp = isoStringToUnixTimestamp(isoTime);
        
        System.out.println("ISO时间: " + isoTime);
        System.out.println("Unix时间戳: " + unixTimestamp);
        
        if (unixTimestamp != null) {
            System.out.println("✅ 时间转换成功");
        } else {
            System.out.println("❌ 时间转换失败");
        }
        
        // 测试JSON数据
        String jsonData = """
            {
                "inpatientInfoId": "PUP00217",
                "name": "马娟",
                "hospitalizationNo": "HUP00217",
                "inhospitalTime": "2025-07-05T09:29:52.833085",
                "outhospitalTime": null,
                "status": 1
            }
            """;
        
        try {
            // 简单的JSON解析测试
            Object obj = JSON.parse(jsonData);
            System.out.println("JSON解析成功: " + obj);
            System.out.println("✅ JSON解析测试通过");
        } catch (Exception e) {
            System.out.println("❌ JSON解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 将ISO格式的时间字符串转换为Unix时间戳
     */
    public static Long isoStringToUnixTimestamp(String isoTimeString) {
        if (isoTimeString == null || isoTimeString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 处理可能的微秒精度，截取到毫秒
            String normalizedTime = isoTimeString;
            if (isoTimeString.contains(".")) {
                String[] parts = isoTimeString.split("\\.");
                if (parts.length == 2) {
                    String fractionalPart = parts[1];
                    // 如果小数部分超过3位，截取前3位（毫秒）
                    if (fractionalPart.length() > 3) {
                        fractionalPart = fractionalPart.substring(0, 3);
                    }
                    normalizedTime = parts[0] + "." + fractionalPart;
                }
            }
            
            // 解析时间字符串
            LocalDateTime dateTime = LocalDateTime.parse(normalizedTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
            // 转换为Unix时间戳（秒）
            return dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
            
        } catch (Exception e) {
            System.err.println("时间转换失败: " + isoTimeString + ", 错误: " + e.getMessage());
            return null;
        }
    }
}
