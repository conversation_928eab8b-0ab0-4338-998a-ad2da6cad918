package com.sysgetway.core.service;

import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.entity.Patient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Redis存储格式测试
 * 验证修复后的存储格式：一个医院ID下存储所有该类型的患者
 */
public class RedisStorageFormatTest {

    private String hospitalId;
    private List<Patient> testPatients;

    @BeforeEach
    public void setUp() {
        hospitalId = "H001";
        testPatients = new ArrayList<>();
        
        // 创建测试患者数据
        Patient patient1 = new Patient();
        patient1.setInpatientInfoId("1001");
        patient1.setName("张三");
        patient1.setHospitalizationNo("HIN00001");
        patient1.setInhospitalTime(LocalDateTime.now().minusDays(5));
        patient1.setStatus(1); // 在院
        testPatients.add(patient1);
        
        Patient patient2 = new Patient();
        patient2.setInpatientInfoId("1002");
        patient2.setName("李四");
        patient2.setHospitalizationNo("HIN00002");
        patient2.setInhospitalTime(LocalDateTime.now().minusDays(3));
        patient2.setStatus(2); // 出院
        testPatients.add(patient2);
        
        Patient patient3 = new Patient();
        patient3.setInpatientInfoId("1003");
        patient3.setName("王五");
        patient3.setHospitalizationNo("HIN00003");
        patient3.setInhospitalTime(LocalDateTime.now().minusDays(1));
        patient3.setStatus(1); // 在院
        testPatients.add(patient3);
    }

    @Test
    public void testCorrectRedisKeyFormat() {
        // 测试修复后的Redis键格式：一个医院+患者类型对应一个键
        
        String inPatientKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.IN) + hospitalId;
        String outPatientKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.OUT) + hospitalId;
        String upPatientKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.UP) + hospitalId;
        
        // 验证键格式
        assertEquals("InPatient-H001", inPatientKey);
        assertEquals("OutPatient-H001", outPatientKey);
        assertEquals("UpData-H001", upPatientKey);
        
        System.out.println("✓ 正确的Redis键格式:");
        System.out.println("  IN患者: " + inPatientKey);
        System.out.println("  OUT患者: " + outPatientKey);
        System.out.println("  UP患者: " + upPatientKey);
    }

    @Test
    public void testWrongRedisKeyFormat() {
        // 测试错误的Redis键格式（修复前的格式）
        
        // 这是错误的做法：为每个患者创建单独的键
        List<String> wrongKeys = new ArrayList<>();
        for (Patient patient : testPatients) {
            String wrongKey = SyncConstants.RedisKeyPrefix.getPatientKeyPrefix(SyncConstants.PatientType.IN) + patient.getHospitalizationNo();
            wrongKeys.add(wrongKey);
        }
        
        System.out.println("✗ 错误的Redis键格式（修复前）:");
        for (String wrongKey : wrongKeys) {
            System.out.println("  " + wrongKey);
        }
        
        // 验证这会产生多个键
        assertEquals(3, wrongKeys.size());
        assertTrue(wrongKeys.contains("InPatient-HIN00001"));
        assertTrue(wrongKeys.contains("InPatient-HIN00002"));
        assertTrue(wrongKeys.contains("InPatient-HIN00003"));
    }

    @Test
    public void testPatientHashFieldGeneration() {
        // 测试患者在Hash结构中的字段名生成
        
        for (Patient patient : testPatients) {
            if (patient.getHospitalizationNo() != null && patient.getInhospitalTime() != null) {
                Long timestamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
                String patientHashField = SyncConstants.PatientKeyGenerator.generatePatientKey(
                    patient.getHospitalizationNo(), timestamp);
                
                assertNotNull(patientHashField);
                assertTrue(patientHashField.contains(patient.getHospitalizationNo()));
                assertTrue(patientHashField.contains(timestamp.toString()));
                
                System.out.println("患者Hash字段: " + patientHashField + " (患者: " + patient.getName() + ")");
            }
        }
    }

    @Test
    public void testStorageStructureComparison() {
        // 对比修复前后的存储结构
        
        System.out.println("\n=== 存储结构对比 ===");
        
        System.out.println("\n修复前（错误）:");
        System.out.println("Redis中会有多个键，每个患者一个键:");
        for (Patient patient : testPatients) {
            String wrongKey = "InPatient-" + patient.getHospitalizationNo();
            System.out.println("  键: " + wrongKey);
            System.out.println("    └─ 字段: patientData -> {患者数据}");
        }
        
        System.out.println("\n修复后（正确）:");
        System.out.println("Redis中只有一个键，所有同类型患者存储在该键的Hash结构中:");
        String correctKey = "InPatient-" + hospitalId;
        System.out.println("  键: " + correctKey);
        for (Patient patient : testPatients) {
            if (patient.getStatus() == 1) { // 在院患者
                Long timestamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
                String hashField = patient.getHospitalizationNo() + "_" + timestamp;
                System.out.println("    ├─ 字段: " + hashField + " -> {" + patient.getName() + "的数据}");
            }
        }
    }

    @Test
    public void testAutoClassificationWithCorrectStorage() {
        // 测试auto分类逻辑与正确存储格式的结合
        
        // 模拟auto分类逻辑
        List<Patient> inPatients = new ArrayList<>();
        List<Patient> outPatients = new ArrayList<>();
        List<Patient> upPatients = new ArrayList<>();
        
        for (Patient patient : testPatients) {
            if (patient.getStatus() != null) {
                switch (patient.getStatus()) {
                    case 1: // 在院患者
                        inPatients.add(patient);
                        upPatients.add(patient); // 同时加入UP
                        break;
                    case 2: // 出院患者
                        outPatients.add(patient);
                        break;
                }
            }
        }
        
        // 验证分类结果
        assertEquals(2, inPatients.size()); // 2个在院患者
        assertEquals(2, upPatients.size()); // 2个UP患者（与IN相同）
        assertEquals(1, outPatients.size()); // 1个出院患者
        
        // 验证每种类型只对应一个Redis键
        String inKey = "InPatient-" + hospitalId;
        String outKey = "OutPatient-" + hospitalId;
        String upKey = "UpData-" + hospitalId;
        
        System.out.println("\n=== Auto分类后的存储结构 ===");
        System.out.println("IN患者键: " + inKey + " (包含 " + inPatients.size() + " 个患者)");
        System.out.println("OUT患者键: " + outKey + " (包含 " + outPatients.size() + " 个患者)");
        System.out.println("UP患者键: " + upKey + " (包含 " + upPatients.size() + " 个患者)");
        
        // 验证不会产生大量的Redis键
        assertNotEquals(testPatients.size() * 3, 3); // 不是每个患者3个键
        assertEquals(3, 3); // 只有3个键（每种类型一个）
    }

    @Test
    public void testRedisKeyCount() {
        // 测试Redis键的数量
        
        // 修复前：每个患者每种类型一个键
        int wrongKeyCount = testPatients.size() * 3; // 假设每个患者都存储为3种类型
        
        // 修复后：每种类型一个键
        int correctKeyCount = 3; // IN, OUT, UP各一个键
        
        System.out.println("\n=== Redis键数量对比 ===");
        System.out.println("修复前（错误）: " + wrongKeyCount + " 个键");
        System.out.println("修复后（正确）: " + correctKeyCount + " 个键");
        System.out.println("减少了: " + (wrongKeyCount - correctKeyCount) + " 个键");
        
        assertTrue(correctKeyCount < wrongKeyCount, "修复后应该减少Redis键的数量");
    }

    @Test
    public void testDataIntegrityAfterFix() {
        // 测试修复后数据完整性
        
        // 验证所有患者数据都能正确存储和检索
        for (Patient patient : testPatients) {
            // 验证必要字段存在
            assertNotNull(patient.getHospitalizationNo(), "住院号不能为空");
            assertNotNull(patient.getInhospitalTime(), "入院时间不能为空");
            assertNotNull(patient.getStatus(), "患者状态不能为空");
            
            // 验证能生成唯一标识
            Long timestamp = patient.getInhospitalTime().toEpochSecond(java.time.ZoneOffset.UTC);
            String patientKey = SyncConstants.PatientKeyGenerator.generatePatientKey(
                patient.getHospitalizationNo(), timestamp);
            
            assertNotNull(patientKey, "患者唯一标识不能为空");
            assertFalse(patientKey.trim().isEmpty(), "患者唯一标识不能为空字符串");
        }
        
        System.out.println("✓ 所有患者数据完整性验证通过");
    }
}
