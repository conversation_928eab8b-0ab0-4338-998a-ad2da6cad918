#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Redis数据格式优化脚本
将Java序列化格式转换为标准JSON格式
"""

import redis
import json
import sys
from typing import Dict, Any, List

class RedisFormatOptimizer:
    def __init__(self, host='localhost', port=6379, db=9, password=None):
        """初始化Redis连接"""
        self.redis_client = redis.Redis(
            host=host, 
            port=port, 
            db=db, 
            password=password,
            decode_responses=True
        )
        
    def test_connection(self) -> bool:
        """测试Redis连接"""
        try:
            self.redis_client.ping()
            print("✅ Redis连接成功")
            return True
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            return False
    
    def parse_java_serialized_data(self, data_str: str) -> Dict[str, Any]:
        """解析Java序列化的数据"""
        try:
            # 尝试解析JSON格式的Java序列化数据
            data = json.loads(data_str)
            
            if isinstance(data, list) and len(data) == 2 and data[0] == "java.util.HashMap":
                # 提取HashMap中的数据
                hashmap_data = data[1]
                return self.convert_java_types(hashmap_data)
            else:
                # 如果已经是普通JSON，直接返回
                return data
                
        except json.JSONDecodeError:
            print(f"⚠️  无法解析数据: {data_str[:100]}...")
            return None
    
    def convert_java_types(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转换Java类型为Python原生类型"""
        converted = {}
        
        for key, value in data.items():
            if isinstance(value, list) and len(value) == 2:
                # 处理Java包装类型，如 ["java.lang.Long", 1750176294]
                java_type, actual_value = value
                if java_type == "java.lang.Long":
                    converted[key] = int(actual_value)
                elif java_type == "java.lang.String":
                    converted[key] = str(actual_value)
                elif java_type == "java.lang.Integer":
                    converted[key] = int(actual_value)
                else:
                    converted[key] = actual_value
            else:
                converted[key] = value
                
        return converted
    
    def format_inpatient_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """按照目标格式重新组织住院信息数据"""
        # 定义字段顺序和默认值
        formatted_data = {
            "name": data.get("name", ""),
            "id_card": data.get("id_card", ""),
            "mobile": data.get("mobile", ""),
            "sex": data.get("sex", ""),
            "age": data.get("age", 0),
            "hospitalization_no": data.get("hospitalization_no", ""),
            "sickbed_no": data.get("sickbed_no", ""),
            "inhospital_time": data.get("inhospital_time", 0),
            "outhospital_time": data.get("outhospital_time", 0),
            "status": data.get("status", 0),
            "category": data.get("category", ""),
            "inpatient_ward": data.get("inpatient_ward", ""),
            "doctor_id": data.get("doctor_id", ""),
            "inpatient_info_id": data.get("inpatient_info_id", "")
        }
        
        # 确保数据类型正确
        if isinstance(formatted_data["age"], str):
            try:
                formatted_data["age"] = int(formatted_data["age"])
            except ValueError:
                formatted_data["age"] = 0
                
        if isinstance(formatted_data["status"], str):
            try:
                formatted_data["status"] = int(formatted_data["status"])
            except ValueError:
                formatted_data["status"] = 0
                
        return formatted_data
    
    def scan_and_optimize_keys(self, pattern: str = "*", batch_size: int = 100) -> Dict[str, int]:
        """扫描并优化匹配的键"""
        stats = {
            "total_scanned": 0,
            "converted": 0,
            "skipped": 0,
            "errors": 0
        }
        
        cursor = 0
        while True:
            cursor, keys = self.redis_client.scan(cursor=cursor, match=pattern, count=batch_size)
            
            for key in keys:
                stats["total_scanned"] += 1
                
                try:
                    # 获取原始数据
                    raw_data = self.redis_client.get(key)
                    if not raw_data:
                        stats["skipped"] += 1
                        continue
                    
                    # 解析Java序列化数据
                    parsed_data = self.parse_java_serialized_data(raw_data)
                    if not parsed_data:
                        stats["skipped"] += 1
                        continue
                    
                    # 格式化为目标格式
                    formatted_data = self.format_inpatient_data(parsed_data)
                    
                    # 存储优化后的数据
                    self.redis_client.set(key, json.dumps(formatted_data, ensure_ascii=False))
                    stats["converted"] += 1
                    
                    if stats["converted"] % 10 == 0:
                        print(f"已转换 {stats['converted']} 条记录...")
                        
                except Exception as e:
                    print(f"❌ 处理键 {key} 时出错: {e}")
                    stats["errors"] += 1
            
            if cursor == 0:
                break
                
        return stats
    
    def preview_conversion(self, key: str) -> None:
        """预览单个键的转换结果"""
        try:
            raw_data = self.redis_client.get(key)
            if not raw_data:
                print(f"键 {key} 不存在")
                return
            
            print(f"\n=== 键: {key} ===")
            print("原始数据:")
            print(raw_data[:200] + "..." if len(raw_data) > 200 else raw_data)
            
            parsed_data = self.parse_java_serialized_data(raw_data)
            if parsed_data:
                formatted_data = self.format_inpatient_data(parsed_data)
                print("\n转换后数据:")
                print(json.dumps(formatted_data, ensure_ascii=False, indent=2))
            else:
                print("❌ 无法解析数据")
                
        except Exception as e:
            print(f"❌ 预览失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Redis数据格式优化工具')
    parser.add_argument('--host', default='localhost', help='Redis主机地址')
    parser.add_argument('--port', type=int, default=6379, help='Redis端口')
    parser.add_argument('--db', type=int, default=9, help='Redis数据库编号')
    parser.add_argument('--password', help='Redis密码')
    parser.add_argument('--pattern', default='*', help='键匹配模式')
    parser.add_argument('--preview', help='预览指定键的转换结果')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小')
    
    args = parser.parse_args()
    
    # 创建优化器
    optimizer = RedisFormatOptimizer(
        host=args.host,
        port=args.port,
        db=args.db,
        password=args.password
    )
    
    # 测试连接
    if not optimizer.test_connection():
        sys.exit(1)
    
    if args.preview:
        # 预览模式
        optimizer.preview_conversion(args.preview)
    else:
        # 批量转换模式
        print(f"开始优化Redis DB{args.db}中的数据...")
        print(f"匹配模式: {args.pattern}")
        
        # 确认操作
        response = input("确认继续吗？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            sys.exit(0)
        
        # 执行优化
        stats = optimizer.scan_and_optimize_keys(args.pattern, args.batch_size)
        
        print("\n=== 优化完成 ===")
        print(f"扫描总数: {stats['total_scanned']}")
        print(f"转换成功: {stats['converted']}")
        print(f"跳过数量: {stats['skipped']}")
        print(f"错误数量: {stats['errors']}")

if __name__ == "__main__":
    main()
